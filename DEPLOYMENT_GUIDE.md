---
id: yendorcats-deployment-complete-guide
title: "YendorCats Complete Deployment Guide"
description: "Comprehensive deployment guide for YendorCats application covering local development, staging, and production environments with AWS ECR integration"
company: "PaceySpace"
author: "<PERSON> Pacey"
owner: "<EMAIL>"
version: "1.0.0"
created: "2025-01-27"
updated: "2025-01-27"
status: "active"
type: "deployment-guide"
project: "yendorcats"
area: "devops/deployment"
environment: ["development", "staging", "production"]
aws_services: ["ECR", "EC2", "S3", "Secrets Manager"]
technologies: ["Docker", "AWS CLI", "bash", ".NET 8", "Node.js", "nginx"]
tags: ["deployment", "ci-cd", "aws", "ecr", "docker", "staging", "production", "automation", "guide"]
---

# YendorCats Complete Deployment Guide

## 🚀 Quick Start

### Prerequisites Checklist
- [ ] **AWS CLI** installed and configured
- [ ] **Docker** installed and running
- [ ] **Git** access to repository
- [ ] **SSH keys** for server access
- [ ] **Environment variables** configured

### 1-Minute Deployment
```bash
# Verify AWS setup
./scripts/aws/verify-aws-setup.sh

# Build and push to ECR
./scripts/deploy/build-and-push.sh

# Deploy to staging
./scripts/deploy/deploy-staging.sh

# Deploy to production (with confirmation)
./scripts/deploy/deploy-production.sh
```

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Environment Setup](#environment-setup)
3. [Local Development](#local-development)
4. [Staging Deployment](#staging-deployment)
5. [Production Deployment](#production-deployment)
6. [Server Management](#server-management)
7. [Troubleshooting](#troubleshooting)
8. [Monitoring & Maintenance](#monitoring--maintenance)

## 🏗️ Architecture Overview

### System Architecture
```mermaid
flowchart TB
    Dev[Developer Machine] -->|git push| GitHub[GitHub Repository]
    GitHub -->|GitHub Actions| ECR[AWS ECR Registry]
    
    subgraph "Local Development"
        DevEnv[Local Environment]
        DevDocker[Docker Compose]
    end
    
    subgraph "AWS Cloud"
        ECR
        StagingEC2[Staging EC2]
        ProductionEC2[Production EC2]
        S3[S3/B2 Storage]
    end
    
    ECR -->|docker pull| StagingEC2
    ECR -->|docker pull| ProductionEC2
    
    StagingEC2 --> S3
    ProductionEC2 --> S3
    
    subgraph "Services"
        API[.NET 8 API]
        Uploader[Node.js Uploader]
        Frontend[nginx Frontend]
    end
```

### Service Components

| Service | Technology | Port | Purpose |
|---------|------------|------|---------|
| **API** | .NET 8 ASP.NET Core | 5003 | Backend REST API |
| **Uploader** | Node.js Express | 5002 | File upload service |
| **Frontend** | nginx + Vanilla JS | 80/443 | Web interface |

### Environment Configuration

| Environment | URL | Purpose | Auto-Deploy |
|-------------|-----|---------|-------------|
| **Development** | localhost | Local development | Manual |
| **Staging** | staging.yendorcats.com | Testing & QA | On `develop` branch |
| **Production** | yendorcats.com | Live website | Manual approval |

## ⚙️ Environment Setup

### 1. AWS CLI Configuration

```bash
# Install AWS CLI (macOS)
brew install awscli

# Configure credentials
aws configure
# AWS Access Key ID: [Your Key]
# AWS Secret Access Key: [Your Secret]
# Default region: ap-southeast-2
# Default output format: json

# Verify setup
./scripts/aws/verify-aws-setup.sh
```

### 2. Docker Setup

```bash
# macOS (OrbStack recommended)
brew install orbstack

# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### 3. Environment Variables

Create `.env` files for each environment:

```bash
# .env.development
ASPNETCORE_ENVIRONMENT=Development
AWS_S3_BUCKET_NAME=yendor
AWS_S3_ACCESS_KEY=your_dev_key
AWS_S3_SECRET_KEY=your_dev_secret
YENDOR_JWT_SECRET=your_dev_jwt_secret

# .env.staging
ASPNETCORE_ENVIRONMENT=Staging
AWS_S3_BUCKET_NAME=yendor
AWS_S3_ACCESS_KEY=your_staging_key
AWS_S3_SECRET_KEY=your_staging_secret
YENDOR_JWT_SECRET=your_staging_jwt_secret
STAGING_EXTERNAL_IP=your_staging_ip

# .env.production
ASPNETCORE_ENVIRONMENT=Production
AWS_S3_BUCKET_NAME=yendor
AWS_S3_ACCESS_KEY=your_prod_key
AWS_S3_SECRET_KEY=your_prod_secret
YENDOR_JWT_SECRET=your_prod_jwt_secret
PRODUCTION_EXTERNAL_IP=your_prod_ip
```

## 💻 Local Development

### Development Workflow

```bash
# Start local development environment
docker-compose up -d

# Or use specific development compose
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Development URLs
- **Frontend**: http://localhost:8080
- **API**: http://localhost:5003
- **Uploader**: http://localhost:5002

### Hot Reload Development

```bash
# Backend (.NET)
cd backend/YendorCats.API
dotnet watch run

# Frontend (served by backend)
# Files in frontend/ are automatically served

# Uploader
cd tools/file-uploader
npm run dev
```

## 🧪 Staging Deployment

### Automatic Staging Deployment

Staging automatically deploys when you push to the `develop` branch:

```bash
# Switch to develop branch
git checkout develop

# Make changes and commit
git add .
git commit -m "Feature: Add new functionality"

# Push to trigger staging deployment
git push origin develop
```

### Manual Staging Deployment

```bash
# Build and push images
./scripts/deploy/build-and-push.sh

# Deploy to staging
./scripts/deploy/deploy-staging.sh [image-tag]

# Examples:
./scripts/deploy/deploy-staging.sh          # Deploy latest
./scripts/deploy/deploy-staging.sh abc123   # Deploy specific commit
```

### Staging Server Setup

```bash
# Initial server setup (run once)
sudo ./scripts/server/setup-server.sh -e staging

# Configure environment
sudo -u yendorcats cp /opt/yendorcats/.env.staging.template /opt/yendorcats/.env.staging
sudo -u yendorcats nano /opt/yendorcats/.env.staging

# Configure AWS credentials
sudo -u yendorcats aws configure
```

### Staging Testing

```bash
# Health checks
curl http://staging.yendorcats.com/health
curl http://staging.yendorcats.com:5003/health
curl http://staging.yendorcats.com:5002/health

# Monitor logs
ssh -i ~/.ssh/yendorcats-staging.pem <EMAIL>
docker-compose -f /opt/yendorcats/docker-compose.staging.yml logs -f
```

## 🚀 Production Deployment

### Production Deployment Process

```bash
# 1. Ensure staging is tested and working
curl http://staging.yendorcats.com/health

# 2. Build and push production images
./scripts/deploy/build-and-push.sh

# 3. Deploy to production (requires confirmation)
./scripts/deploy/deploy-production.sh [image-tag]

# The script will:
# - Show deployment warning
# - Require confirmation: "DEPLOY TO PRODUCTION"
# - Create backup of current deployment
# - Deploy new version
# - Run health checks
```

### Production Server Setup

```bash
# Initial server setup (run once)
sudo ./scripts/server/setup-server.sh -e production

# Configure environment
sudo -u yendorcats cp /opt/yendorcats/.env.production.template /opt/yendorcats/.env.production
sudo -u yendorcats nano /opt/yendorcats/.env.production

# Configure AWS credentials
sudo -u yendorcats aws configure

# Setup SSL certificates (if needed)
sudo certbot --nginx -d yendorcats.com -d www.yendorcats.com
```

### Production Safety Features

- **Confirmation Required**: Must type "DEPLOY TO PRODUCTION"
- **Automatic Backup**: Creates backup before deployment
- **Health Checks**: Verifies services after deployment
- **Rollback Ready**: Backup path provided for quick rollback

## 🖥️ Server Management

### Server-Side Operations

```bash
# Pull and deploy (run on server)
./scripts/server/pull-and-deploy.sh [image-tag]

# Examples:
./scripts/server/pull-and-deploy.sh                    # Deploy latest
./scripts/server/pull-and-deploy.sh abc123             # Deploy specific commit
./scripts/server/pull-and-deploy.sh -e staging         # Deploy to staging
./scripts/server/pull-and-deploy.sh --skip-health      # Skip health checks
```

### Container Management

```bash
# View running containers
docker-compose ps

# View logs
docker-compose logs -f [service]

# Restart service
docker-compose restart [service]

# Update single service
docker-compose pull [service]
docker-compose up -d [service]

# Stop all services
docker-compose down

# Start all services
docker-compose up -d
```

### System Monitoring

```bash
# System resources
/opt/yendorcats/scripts/monitor.sh

# Container stats
docker stats

# Disk usage
df -h
docker system df

# Clean up old images
./scripts/utils/cleanup-images.sh
```

## 🔧 Troubleshooting

### Common Issues

#### 1. ECR Authentication Failed
```bash
# Re-authenticate with ECR
./scripts/aws/ecr-login.sh

# Check AWS credentials
aws sts get-caller-identity
```

#### 2. Container Won't Start
```bash
# Check logs
docker logs [container-name]

# Check environment variables
docker exec [container-name] env

# Check health status
docker inspect [container-name] | grep Health
```

#### 3. Image Pull Failed
```bash
# Check ECR repositories
aws ecr describe-repositories --region ap-southeast-2

# Check image exists
aws ecr list-images --repository-name yendorcats-api --region ap-southeast-2

# Manual pull test
docker pull ************.dkr.ecr.ap-southeast-2.amazonaws.com/yendorcats-api:latest
```

#### 4. Health Check Failed
```bash
# Test health endpoints
curl -v http://localhost/health
curl -v http://localhost:5003/health
curl -v http://localhost:5002/health

# Check container logs
docker-compose logs api
docker-compose logs uploader
docker-compose logs frontend
```

### Debug Commands

```bash
# Container inspection
docker inspect [container-name]

# Network inspection
docker network ls
docker network inspect yendorcats-production

# Volume inspection
docker volume ls
docker volume inspect yendorcats-production-api-data

# System information
docker system info
docker system df
```

## 📊 Monitoring & Maintenance

### Health Monitoring

```bash
# Automated health checks
./scripts/server/health-check.sh

# Manual health checks
curl https://yendorcats.com/health
curl https://yendorcats.com:5003/health
curl https://yendorcats.com:5002/health
```

### Log Management

```bash
# View recent logs
docker-compose logs --tail=100

# Follow logs in real-time
docker-compose logs -f

# Service-specific logs
docker-compose logs -f api
docker-compose logs -f uploader
docker-compose logs -f frontend

# Export logs
docker-compose logs > deployment-logs-$(date +%Y%m%d).log
```

### Backup & Recovery

```bash
# Create backup
./scripts/utils/backup-data.sh

# Restore from backup
./scripts/deploy/rollback.sh /opt/yendorcats/backups/20250127-143022

# Database backup (if using external DB)
mysqldump -h db-host -u user -p yendorcats > backup.sql
```

### Cleanup & Optimization

```bash
# Clean up old Docker images
./scripts/utils/cleanup-images.sh

# Clean up with dry run first
./scripts/utils/cleanup-images.sh --dry-run

# Keep more images
./scripts/utils/cleanup-images.sh --keep 10

# Clean up system
docker system prune -a
```

## 📞 Support & Resources

### Getting Help

- **Documentation**: Check this guide and script `--help` flags
- **Logs**: Always check container logs first
- **Health Checks**: Use provided health check scripts
- **AWS Issues**: Verify credentials and permissions

### Useful Commands Reference

```bash
# Quick status check
docker-compose ps && docker stats --no-stream

# Quick deployment
./scripts/deploy/build-and-push.sh && ./scripts/deploy/deploy-staging.sh

# Emergency stop
docker-compose down

# Emergency restart
docker-compose restart

# View all logs
docker-compose logs --tail=50
```

### Contact Information

- **Technical Support**: <EMAIL>
- **AWS Account**: ************
- **Region**: ap-southeast-2 (Australia)

---

### Tags
#deployment #ci-cd #aws #ecr #docker #staging #production #automation #guide #yendorcats #devops

---
