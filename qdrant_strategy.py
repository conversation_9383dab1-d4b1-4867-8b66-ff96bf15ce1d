"""
Qdrant Multi-Tenant Collection Strategy for Shared AI Agent Context

This module defines the collection strategy for a multi-agent embedding system
where different AI agents (<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>) can share context
while maintaining isolation when needed.
"""

from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
import hashlib


class CollectionStrategy(Enum):
    """Collection strategy types"""
    AGENT_SPECIFIC = "agent_specific"    # Agent-specific collections
    MODEL_SPECIFIC = "model_specific"    # Model-specific collections
    SHARED_GLOBAL = "shared_global"      # Global shared collections
    HYBRID = "hybrid"                    # Mix of shared and isolated


class ContentType(Enum):
    """Content types for specialized collections"""
    DOCS = "docs"                       # Documentation and text files
    CODE = "code"                       # Source code files
    CONVERSATIONS = "conversations"      # Chat/conversation history
    KNOWLEDGE = "knowledge"             # Curated knowledge base
    PROJECTS = "projects"               # Project-specific content


@dataclass
class CollectionConfig:
    """Configuration for a single collection"""
    name: str
    strategy: CollectionStrategy
    content_type: ContentType
    embedding_model: str
    agents: Set[str]
    vector_size: int
    distance_metric: str = "cosine"
    description: str = ""
    max_points: Optional[int] = None
    retention_days: Optional[int] = None


class QdrantMultiTenantStrategy:
    """
    Multi-tenant strategy for Qdrant collections supporting shared context
    across multiple AI agents while maintaining flexibility and performance.
    """

    def __init__(self):
        self.agents = {
            "claude": "Claude Code AI Assistant",
            "auggie": "Augment Code Assistant",
            "gemini": "Google Gemini",
            "qwen": "Qwen Assistant",
            "cline": "Cline VS Code Assistant",
            "cursor": "Cursor AI Assistant"
        }

        self.embedding_models = {
            "dengcao/Qwen3-Embedding-8B:Q4_K_M": {
                "size": 4096,
                "description": "Qwen3 8B - High quality embeddings (4096 dims)",
                "use_case": "Critical documents, knowledge base"
            },
            "dengcao/Qwen3-Embedding-4B:Q4_K_M": {
                "size": 2560,
                "description": "Qwen3 4B - Balanced quality/performance (2560 dims)",
                "use_case": "General purpose embeddings"
            },
            "dengcao/Qwen3-Embedding-0.6B:Q8_0": {
                "size": 1024,
                "description": "Qwen3 0.6B - Fast embeddings (1024 dims)",
                "use_case": "Rapid indexing, large datasets"
            }
        }

    def get_collection_configs(self) -> List[CollectionConfig]:
        """
        Define the complete collection strategy.

        Strategy breakdown:
        1. Shared global collections for common knowledge
        2. Model-specific collections for cross-agent compatibility
        3. Agent-specific collections for private context
        4. Content-type specific collections for organization
        """
        configs = []

        # 1. SHARED GLOBAL COLLECTIONS - Cross-agent knowledge sharing
        shared_collections = [
            CollectionConfig(
                name="shared_knowledge_8b",
                strategy=CollectionStrategy.SHARED_GLOBAL,
                content_type=ContentType.KNOWLEDGE,
                embedding_model="dengcao/Qwen3-Embedding-8B:Q4_K_M",
                agents=set(self.agents.keys()),
                vector_size=4096,
                description="High-quality shared knowledge base accessible to all agents",
                max_points=1000000,
                retention_days=365
            ),
            CollectionConfig(
                name="shared_docs_4b",
                strategy=CollectionStrategy.SHARED_GLOBAL,
                content_type=ContentType.DOCS,
                embedding_model="dengcao/Qwen3-Embedding-4B:Q4_K_M",
                agents=set(self.agents.keys()),
                vector_size=2560,
                description="Shared documentation accessible to all agents",
                max_points=500000,
                retention_days=180
            ),
            CollectionConfig(
                name="shared_code_4b",
                strategy=CollectionStrategy.SHARED_GLOBAL,
                content_type=ContentType.CODE,
                embedding_model="dengcao/Qwen3-Embedding-4B:Q4_K_M",
                agents=set(self.agents.keys()),
                vector_size=2560,
                description="Shared codebase knowledge for all coding agents",
                max_points=750000,
                retention_days=90
            )
        ]
        configs.extend(shared_collections)

        # 2. MODEL-SPECIFIC COLLECTIONS - Cross-agent but model-isolated
        for model_name, model_info in self.embedding_models.items():
            model_short = self._get_model_short_name(model_name)

            # Fast search collection for each model
            configs.append(CollectionConfig(
                name=f"search_{model_short}",
                strategy=CollectionStrategy.MODEL_SPECIFIC,
                content_type=ContentType.DOCS,
                embedding_model=model_name,
                agents=set(self.agents.keys()),
                vector_size=model_info["size"],
                description=f"Fast search collection using {model_info['description']}",
                max_points=250000,
                retention_days=30
            ))

        # 3. AGENT-SPECIFIC COLLECTIONS - Private agent context
        for agent in self.agents.keys():
            # Conversation history (private to each agent)
            configs.append(CollectionConfig(
                name=f"{agent}_conversations_4b",
                strategy=CollectionStrategy.AGENT_SPECIFIC,
                content_type=ContentType.CONVERSATIONS,
                embedding_model="dengcao/Qwen3-Embedding-4B:Q4_K_M",
                agents={agent},
                vector_size=2560,
                description=f"Private conversation history for {agent}",
                max_points=100000,
                retention_days=60
            ))

            # Agent-specific workspace
            configs.append(CollectionConfig(
                name=f"{agent}_workspace_0_6b",
                strategy=CollectionStrategy.AGENT_SPECIFIC,
                content_type=ContentType.PROJECTS,
                embedding_model="dengcao/Qwen3-Embedding-0.6B:Q8_0",
                agents={agent},
                vector_size=1024,
                description=f"Fast workspace indexing for {agent}",
                max_points=200000,
                retention_days=30
            ))

        # 4. PROJECT-SPECIFIC COLLECTIONS
        project_collections = [
            CollectionConfig(
                name="paceyspace_projects_8b",
                strategy=CollectionStrategy.HYBRID,
                content_type=ContentType.PROJECTS,
                embedding_model="dengcao/Qwen3-Embedding-8B:Q4_K_M",
                agents={"claude", "auggie", "cursor"},  # Web dev agents
                vector_size=4096,
                description="PaceySpace Projects (web development) context",
                max_points=300000,
                retention_days=180
            ),
            CollectionConfig(
                name="paceyspace_labs_4b",
                strategy=CollectionStrategy.HYBRID,
                content_type=ContentType.PROJECTS,
                embedding_model="dengcao/Qwen3-Embedding-4B:Q4_K_M",
                agents={"claude", "qwen", "cline"},  # DevOps/Infrastructure agents
                vector_size=2560,
                description="PaceySpace Labs (DevOps, CI/CD, infrastructure) context",
                max_points=200000,
                retention_days=120
            )
        ]
        configs.extend(project_collections)

        return configs

    def get_collection_for_context(self, agent: str, content_type: ContentType,
                                 model: str, shared: bool = True) -> str:
        """
        Get the appropriate collection name for storing/retrieving context.

        Args:
            agent: The requesting agent
            content_type: Type of content being stored/retrieved
            model: Embedding model being used
            shared: Whether to prefer shared collections

        Returns:
            Collection name to use
        """
        model_short = self._get_model_short_name(model)

        # Priority order for collection selection - MODEL-AWARE ROUTING
        if shared and content_type == ContentType.KNOWLEDGE:
            if "8B" in model:
                return "shared_knowledge_8b"
            elif "4B" in model:
                return "shared_docs_4b"  # Use docs collection for 4B knowledge
            else:  # 0.6B model
                return f"search_{model_short}"
        elif shared and content_type == ContentType.DOCS:
            if "8B" in model:
                return "shared_knowledge_8b"  # Use knowledge collection for 8B docs
            elif "4B" in model:
                return "shared_docs_4b"
            else:  # 0.6B model
                return f"search_{model_short}"
        elif shared and content_type == ContentType.CODE:
            if "8B" in model or "4B" in model:
                return "shared_code_4b"  # Both 8B and 4B use this collection
            else:  # 0.6B model
                return f"search_{model_short}"
        elif content_type == ContentType.CONVERSATIONS:
            return f"{agent}_conversations_4b"
        elif content_type == ContentType.PROJECTS:
            # Route to appropriate project collection based on agent
            if agent in {"claude", "auggie", "cursor"}:
                return "paceyspace_projects_8b"
            elif agent in {"claude", "qwen", "cline"}:
                return "paceyspace_labs_4b"
            else:
                return f"{agent}_workspace_0_6b"
        else:
            # Default to model-specific search collection
            return f"search_{model_short}"

    def get_cross_agent_search_collections(self, agent: str,
                                         content_type: ContentType) -> List[str]:
        """
        Get list of collections to search across for cross-agent context retrieval.

        This enables agents to benefit from knowledge created by other agents
        while respecting privacy boundaries.
        """
        collections = []

        # Always include shared collections
        if content_type in {ContentType.KNOWLEDGE, ContentType.DOCS, ContentType.CODE}:
            collections.extend([
                "shared_knowledge_8b",
                "shared_docs_4b",
                "shared_code_4b"
            ])

        # Add project-specific collections based on agent access
        if agent in {"claude", "auggie", "cursor"}:
            collections.append("paceyspace_projects_8b")
        if agent in {"claude", "qwen", "cline"}:
            collections.append("paceyspace_labs_4b")

        # Add model-specific search collections
        for model_name in self.embedding_models.keys():
            model_short = self._get_model_short_name(model_name)
            collections.append(f"search_{model_short}")

        return list(set(collections))  # Remove duplicates

    def get_storage_strategy(self, content_size: int, importance: str) -> Tuple[str, str]:
        """
        Recommend embedding model and collection strategy based on content characteristics.

        Args:
            content_size: Size of content in characters
            importance: "high", "medium", "low"

        Returns:
            (recommended_model, strategy_explanation)
        """
        if importance == "high" or content_size < 10000:
            # Use high-quality model for important or small content
            return (
                "dengcao/Qwen3-Embedding-8B:Q4_K_M",
                "High-quality embeddings for important content"
            )
        elif content_size < 100000:
            # Balanced model for medium content
            return (
                "dengcao/Qwen3-Embedding-4B:Q4_K_M",
                "Balanced quality/performance for medium content"
            )
        else:
            # Fast model for large content
            return (
                "dengcao/Qwen3-Embedding-0.6B:Q8_0",
                "Fast processing for large content volumes"
            )

    def _get_model_short_name(self, model_name: str) -> str:
        """Get short name for model"""
        if "8B" in model_name:
            return "qwen3_8b"
        elif "4B" in model_name:
            return "qwen3_4b"
        elif "0.6B" in model_name:
            return "qwen3_0_6b"
        else:
            return model_name.replace("/", "_").replace(":", "_").lower()

    def get_collection_summary(self) -> Dict:
        """Get summary of the collection strategy"""
        configs = self.get_collection_configs()

        summary = {
            "total_collections": len(configs),
            "by_strategy": {},
            "by_content_type": {},
            "by_model": {},
            "shared_collections": [],
            "agent_specific_collections": {},
            "cross_agent_accessible": []
        }

        for config in configs:
            # Count by strategy
            strategy_name = config.strategy.value
            summary["by_strategy"][strategy_name] = summary["by_strategy"].get(strategy_name, 0) + 1

            # Count by content type
            content_name = config.content_type.value
            summary["by_content_type"][content_name] = summary["by_content_type"].get(content_name, 0) + 1

            # Count by model
            model_short = self._get_model_short_name(config.embedding_model)
            summary["by_model"][model_short] = summary["by_model"].get(model_short, 0) + 1

            # Track shared collections
            if config.strategy == CollectionStrategy.SHARED_GLOBAL:
                summary["shared_collections"].append(config.name)

            # Track agent-specific collections
            if config.strategy == CollectionStrategy.AGENT_SPECIFIC:
                for agent in config.agents:
                    if agent not in summary["agent_specific_collections"]:
                        summary["agent_specific_collections"][agent] = []
                    summary["agent_specific_collections"][agent].append(config.name)

            # Track cross-agent accessible collections
            if len(config.agents) > 1:
                summary["cross_agent_accessible"].append(config.name)

        return summary


def print_strategy_summary():
    """Print a summary of the collection strategy"""
    strategy = QdrantMultiTenantStrategy()
    configs = strategy.get_collection_configs()
    summary = strategy.get_collection_summary()

    print("🏗️  Qdrant Multi-Tenant Collection Strategy")
    print("=" * 60)

    print(f"\n📊 Overview:")
    print(f"  Total Collections: {summary['total_collections']}")
    print(f"  Shared Collections: {len(summary['shared_collections'])}")
    print(f"  Cross-Agent Accessible: {len(summary['cross_agent_accessible'])}")

    print(f"\n📚 By Strategy:")
    for strategy_type, count in summary['by_strategy'].items():
        print(f"  {strategy_type}: {count} collections")

    print(f"\n📁 By Content Type:")
    for content_type, count in summary['by_content_type'].items():
        print(f"  {content_type}: {count} collections")

    print(f"\n🤖 By Model:")
    for model, count in summary['by_model'].items():
        print(f"  {model}: {count} collections")

    print(f"\n🌐 Shared Collections (all agents):")
    for collection in summary['shared_collections']:
        print(f"  - {collection}")

    print(f"\n🔐 Agent-Specific Collections:")
    for agent, collections in summary['agent_specific_collections'].items():
        print(f"  {agent}: {len(collections)} collections")

    print(f"\n🔍 Cross-Agent Search Example (claude + docs):")
    search_collections = strategy.get_cross_agent_search_collections("claude", ContentType.DOCS)
    for collection in search_collections[:5]:  # Show first 5
        print(f"  - {collection}")
    if len(search_collections) > 5:
        print(f"  ... and {len(search_collections) - 5} more")


if __name__ == "__main__":
    print_strategy_summary()