#!/usr/bin/env python3
"""
Batch Embedding System for Multiple Projects

This system automatically processes multiple projects with intelligent routing
to appropriate collections based on project type, content, and sharing requirements.
"""

import os
import sys
import yaml
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from datetime import datetime
import re

from embed_documents import DocumentEmbedder
from qdrant_strategy import QdrantMultiTenantStrategy, ContentType


@dataclass
class ProjectConfig:
    """Configuration for a single project"""
    name: str
    path: str
    type: str  # "web", "devops", "ai", "library", "client", "personal"
    sharing: str  # "shared", "private", "project-specific"
    agent: str  # "claude", "auggie", "gemini", "qwen", etc.
    model: str  # embedding model to use
    priority: str  # "high", "medium", "low"
    exclude_patterns: List[str] = None
    include_patterns: List[str] = None
    tags: List[str] = None

    def __post_init__(self):
        if self.exclude_patterns is None:
            self.exclude_patterns = []
        if self.include_patterns is None:
            self.include_patterns = []
        if self.tags is None:
            self.tags = []


class ProjectClassifier:
    """Automatically classifies projects based on their content and structure"""

    def __init__(self):
        self.web_indicators = {
            'files': ['package.json', 'tsconfig.json', 'webpack.config.js', 'vite.config.js'],
            'directories': ['src', 'components', 'pages', 'public', 'static'],
            'extensions': ['.tsx', '.jsx', '.vue', '.svelte'],
            'content_patterns': ['react', 'vue', 'angular', 'express', 'fastapi']
        }

        self.devops_indicators = {
            'files': ['Dockerfile', 'docker-compose.yml', 'Makefile', 'terraform.tf'],
            'directories': ['.github', '.gitlab-ci', 'infrastructure', 'deploy'],
            'extensions': ['.tf', '.yml', '.yaml'],
            'content_patterns': ['kubernetes', 'terraform', 'ansible', 'jenkins']
        }

        self.ai_indicators = {
            'files': ['requirements.txt', 'pyproject.toml', 'model.py'],
            'directories': ['models', 'training', 'inference', 'datasets'],
            'extensions': ['.ipynb', '.py'],
            'content_patterns': ['tensorflow', 'pytorch', 'sklearn', 'transformers', 'langchain']
        }

        self.library_indicators = {
            'files': ['setup.py', 'pyproject.toml', 'Cargo.toml', 'go.mod'],
            'directories': ['lib', 'src', 'pkg'],
            'content_patterns': ['import', 'export', 'module', 'package']
        }

    def classify_project(self, project_path: Path) -> Tuple[str, float]:
        """
        Classify a project and return (type, confidence_score)
        """
        if not project_path.exists():
            return "unknown", 0.0

        scores = {
            "web": self._score_project(project_path, self.web_indicators),
            "devops": self._score_project(project_path, self.devops_indicators),
            "ai": self._score_project(project_path, self.ai_indicators),
            "library": self._score_project(project_path, self.library_indicators)
        }

        # Default patterns for other types
        if any(name in project_path.name.lower() for name in ['client', 'customer', 'proprietary']):
            scores["client"] = 0.8

        if any(name in project_path.name.lower() for name in ['personal', 'private', 'draft']):
            scores["personal"] = 0.7

        # Find highest scoring type
        best_type = max(scores.items(), key=lambda x: x[1])
        return best_type[0], best_type[1]

    def _score_project(self, project_path: Path, indicators: Dict) -> float:
        """Score a project against a set of indicators"""
        score = 0.0
        total_possible = 0

        # Check for indicator files
        for file_name in indicators.get('files', []):
            total_possible += 1
            if (project_path / file_name).exists():
                score += 1

        # Check for indicator directories
        for dir_name in indicators.get('directories', []):
            total_possible += 1
            if (project_path / dir_name).exists():
                score += 1

        # Check for file extensions
        if indicators.get('extensions'):
            total_possible += 1
            if any(project_path.glob(f"**/*{ext}") for ext in indicators['extensions']):
                score += 1

        # Check content patterns (simple check in common files)
        if indicators.get('content_patterns'):
            total_possible += 1
            if self._check_content_patterns(project_path, indicators['content_patterns']):
                score += 1

        return score / total_possible if total_possible > 0 else 0.0

    def _check_content_patterns(self, project_path: Path, patterns: List[str]) -> bool:
        """Check if any content patterns exist in common files"""
        check_files = ['README.md', 'package.json', 'requirements.txt', 'pyproject.toml']

        for file_name in check_files:
            file_path = project_path / file_name
            if file_path.exists():
                try:
                    content = file_path.read_text(encoding='utf-8', errors='ignore').lower()
                    if any(pattern.lower() in content for pattern in patterns):
                        return True
                except:
                    continue

        return False


class BatchEmbeddingSystem:
    """
    Batch system for embedding multiple projects with intelligent routing
    """

    def __init__(self, config_file: Optional[str] = None):
        self.embedder = DocumentEmbedder()
        self.strategy = QdrantMultiTenantStrategy()
        self.classifier = ProjectClassifier()
        self.config_file = config_file or "embedding_projects.yaml"
        self.projects: List[ProjectConfig] = []

    def load_projects_config(self) -> bool:
        """Load projects configuration from YAML file"""
        config_path = Path(self.config_file)

        if not config_path.exists():
            print(f"Config file {self.config_file} not found. Creating template...")
            self.create_template_config()
            return False

        try:
            with open(config_path, 'r') as f:
                data = yaml.safe_load(f)

            self.projects = []
            for project_data in data.get('projects', []):
                self.projects.append(ProjectConfig(**project_data))

            print(f"Loaded {len(self.projects)} projects from {self.config_file}")
            return True

        except Exception as e:
            print(f"Error loading config: {e}")
            return False

    def create_template_config(self):
        """Create a template configuration file"""
        template = {
            "projects": [
                {
                    "name": "MCP Servers",
                    "path": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP",
                    "type": "library",
                    "sharing": "shared",
                    "agent": "claude",
                    "model": "dengcao/Qwen3-Embedding-8B:Q4_K_M",
                    "priority": "high",
                    "exclude_patterns": ["*.pyc", "__pycache__", ".git", "node_modules", ".venv"],
                    "tags": ["mcp", "infrastructure", "embedding"]
                },
                {
                    "name": "PaceySpace Projects",
                    "path": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/PaceySpace-Projects",
                    "type": "web",
                    "sharing": "shared",
                    "agent": "claude",
                    "model": "dengcao/Qwen3-Embedding-4B:Q4_K_M",
                    "priority": "high",
                    "exclude_patterns": ["node_modules", "dist", "build"],
                    "tags": ["web", "frontend", "react"]
                },
                {
                    "name": "PaceySpace Labs",
                    "path": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/PaceySpace-Labs",
                    "type": "devops",
                    "sharing": "project-specific",
                    "agent": "claude",
                    "model": "dengcao/Qwen3-Embedding-4B:Q4_K_M",
                    "priority": "medium",
                    "exclude_patterns": [".terraform", "*.tfstate"],
                    "tags": ["devops", "infrastructure", "automation"]
                },
                {
                    "name": "Client Project Alpha",
                    "path": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/Client-Alpha",
                    "type": "client",
                    "sharing": "private",
                    "agent": "claude",
                    "model": "dengcao/Qwen3-Embedding-0.6B:Q8_0",
                    "priority": "medium",
                    "tags": ["client", "confidential"]
                }
            ]
        }

        with open(self.config_file, 'w') as f:
            yaml.dump(template, f, default_flow_style=False, indent=2)

        print(f"Created template config at {self.config_file}")
        print("Edit this file to configure your projects, then run again.")

    def scan_projects_directory(self, base_path: str) -> List[ProjectConfig]:
        """
        Automatically scan a directory and create project configurations
        """
        base = Path(base_path)
        if not base.exists():
            print(f"Directory {base_path} does not exist")
            return []

        auto_projects = []

        for project_dir in base.iterdir():
            if not project_dir.is_dir() or project_dir.name.startswith('.'):
                continue

            # Classify the project
            project_type, confidence = self.classifier.classify_project(project_dir)

            if confidence < 0.3:  # Skip if confidence is too low
                continue

            # Determine sharing and model based on type
            sharing, model, agent = self._get_defaults_for_type(project_type)

            config = ProjectConfig(
                name=project_dir.name,
                path=str(project_dir),
                type=project_type,
                sharing=sharing,
                agent=agent,
                model=model,
                priority="medium",
                exclude_patterns=self._get_default_excludes(project_type),
                tags=[project_type, "auto-detected"]
            )

            auto_projects.append(config)

        return auto_projects

    def _get_defaults_for_type(self, project_type: str) -> Tuple[str, str, str]:
        """Get default sharing, model, and agent for project type"""
        defaults = {
            "web": ("shared", "dengcao/Qwen3-Embedding-4B:Q4_K_M", "claude"),
            "devops": ("project-specific", "dengcao/Qwen3-Embedding-4B:Q4_K_M", "claude"),
            "ai": ("shared", "dengcao/Qwen3-Embedding-8B:Q4_K_M", "claude"),
            "library": ("shared", "dengcao/Qwen3-Embedding-8B:Q4_K_M", "claude"),
            "client": ("private", "dengcao/Qwen3-Embedding-0.6B:Q8_0", "claude"),
            "personal": ("private", "dengcao/Qwen3-Embedding-0.6B:Q8_0", "claude")
        }
        return defaults.get(project_type, ("shared", "dengcao/Qwen3-Embedding-4B:Q4_K_M", "claude"))

    def _get_default_excludes(self, project_type: str) -> List[str]:
        """Get default exclude patterns for project type"""
        common = ["*.pyc", "__pycache__", ".git", ".DS_Store", "*.log"]

        type_specific = {
            "web": ["node_modules", "dist", "build", ".next", ".nuxt"],
            "devops": [".terraform", "*.tfstate", "*.tfstate.backup"],
            "ai": ["*.model", "*.ckpt", "datasets/*", "__pycache__"],
            "client": ["*.env", "*.key", "secrets/*"]
        }

        return common + type_specific.get(project_type, [])

    def process_all_projects(self, dry_run: bool = False) -> Dict[str, int]:
        """
        Process all configured projects
        """
        if not self.projects:
            print("No projects configured. Load configuration first.")
            return {}

        results = {
            "processed": 0,
            "successful": 0,
            "failed": 0,
            "skipped": 0
        }

        # Sort by priority
        priority_order = {"high": 0, "medium": 1, "low": 2}
        sorted_projects = sorted(self.projects, key=lambda p: priority_order.get(p.priority, 3))

        for project in sorted_projects:
            print(f"\n{'='*60}")
            print(f"🚀 Processing: {project.name}")
            print(f"📁 Path: {project.path}")
            print(f"🏷️  Type: {project.type}, Sharing: {project.sharing}")
            print(f"🤖 Agent: {project.agent}, Model: {project.model}")

            if dry_run:
                print("   [DRY RUN - Would process this project]")
                results["processed"] += 1
                continue

            try:
                success = self.process_single_project(project)
                results["processed"] += 1

                if success:
                    results["successful"] += 1
                    print(f"✅ Successfully processed {project.name}")
                else:
                    results["failed"] += 1
                    print(f"❌ Failed to process {project.name}")

            except Exception as e:
                print(f"💥 Error processing {project.name}: {e}")
                results["failed"] += 1

        return results

    def process_single_project(self, project: ProjectConfig) -> bool:
        """Process a single project"""
        project_path = Path(project.path)

        if not project_path.exists():
            print(f"❌ Project path does not exist: {project.path}")
            return False

        # Determine sharing mode
        shared = project.sharing == "shared"

        # Handle include/exclude patterns
        extensions = None
        if project.include_patterns:
            # Extract file extensions from include patterns
            extensions = []
            for pattern in project.include_patterns:
                if pattern.startswith("*."):
                    extensions.append(pattern[1:])  # Remove the *
                elif "." in pattern and not "/" in pattern:
                    if pattern.startswith("."):
                        extensions.append(pattern)
                    else:
                        extensions.append("." + pattern.split(".")[-1])

        # Process the project
        if project_path.is_file():
            return self.embedder.embed_file(project_path, project.agent, project.model, shared)
        else:
            files_processed, files_success = self.embedder.embed_directory(
                project_path, project.agent, project.model, shared, extensions
            )
            print(f"📊 Processed {files_success}/{files_processed} files")
            return files_success > 0

    def generate_embedding_report(self) -> Dict:
        """Generate a comprehensive report of the embedding system"""
        collections = self.embedder.list_collections()

        report = {
            "timestamp": datetime.now().isoformat(),
            "total_collections": len(collections),
            "total_points": sum(col["points_count"] for col in collections),
            "collections": {},
            "projects_configured": len(self.projects),
            "embedding_models": list(self.strategy.embedding_models.keys())
        }

        # Analyze collections
        for col in collections:
            report["collections"][col["name"]] = {
                "points": col["points_count"],
                "estimated_size_mb": col["points_count"] * 4096 * 4 / (1024 * 1024)
            }

        return report


def main():
    """Main CLI interface"""
    import argparse

    parser = argparse.ArgumentParser(description="Batch Embedding System for Multiple Projects")
    parser.add_argument("--config", default="embedding_projects.yaml", help="Config file path")
    parser.add_argument("--scan", help="Scan directory for projects")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be processed")
    parser.add_argument("--report", action="store_true", help="Generate embedding report")

    args = parser.parse_args()

    system = BatchEmbeddingSystem(args.config)

    if args.scan:
        print(f"🔍 Scanning {args.scan} for projects...")
        auto_projects = system.scan_projects_directory(args.scan)

        print(f"Found {len(auto_projects)} projects:")
        for project in auto_projects:
            print(f"  - {project.name} ({project.type}) - {project.sharing}")

        if auto_projects:
            save = input("\nSave these projects to config? (y/N): ").strip().lower()
            if save == 'y':
                system.projects = auto_projects
                # Save to config file
                projects_data = [asdict(p) for p in auto_projects]
                config_data = {"projects": projects_data}

                with open(args.config, 'w') as f:
                    yaml.dump(config_data, f, default_flow_style=False, indent=2)

                print(f"💾 Saved {len(auto_projects)} projects to {args.config}")

    elif args.report:
        if system.load_projects_config():
            report = system.generate_embedding_report()
            print(json.dumps(report, indent=2))

    else:
        # Process projects
        if system.load_projects_config():
            results = system.process_all_projects(dry_run=args.dry_run)

            print(f"\n📊 Batch Processing Results:")
            print(f"   Processed: {results['processed']}")
            print(f"   Successful: {results['successful']}")
            print(f"   Failed: {results['failed']}")
            print(f"   Skipped: {results['skipped']}")


if __name__ == "__main__":
    main()