version: '3.8'

services:
  # MariaDB Database
  db:
    image: mariadb:10.11
    container_name: yendorcats-db-test
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_USER=${MY<PERSON>QL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=YendorCats
    ports:
      - "3306:3306"
    volumes:
      - mariadb-test-data:/var/lib/mysql
    networks:
      - yendorcats-test-network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # File Upload Service
  uploader:
    image: yendorcats/uploader:local
    container_name: yendorcats-uploader-test
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME:-yendor}
      - AWS_S3_REGION=us-west-004
      - AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      - API_BASE_URL=http://localhost:5003
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yendorcats-test-network

  # Simple Frontend (static files served by nginx)
  frontend:
    image: nginx:alpine
    container_name: yendorcats-frontend-test
    ports:
      - "80:80"
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 5s
    networks:
      - yendorcats-test-network

networks:
  yendorcats-test-network:
    driver: bridge
    name: yendorcats-test

volumes:
  mariadb-test-data:
    driver: local
