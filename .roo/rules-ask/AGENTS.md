# AGENTS.md

This file provides guidance to agents when working with code in this repository.

# Project Documentation Rules (Non-Obvious Only)

- Two AGENTS docs exist; the canonical one for coding agents is [AGENTS.md](AGENTS.md). The emoji-titled doc [🛡️ AGENTS.md](🛡️ AGENTS.md) is legacy/general; do not follow it for build/run rules.
- The Qdrant MCP server is configured by env only (no CLI flags besides --transport). Canonical references: [MCP/mcp-server-qdrant/README.md](MCP/mcp-server-qdrant/README.md), [MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py](MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py).
- The repository contains two unrelated embedding stacks:
  - MCP server uses FastEmbed (MiniLM-L6-v2, 384 dims) — see [MCP/mcp-server-qdrant](MCP/mcp-server-qdrant/README.md).
  - Top-level embedding tools use Ollama Qwen (4096/2560/1024 dims) — read [EMBEDDING_GUIDE.md](EMBEDDING_GUIDE.md) and [EMBEDDING_USAGE.md](EMBEDDING_USAGE.md).
  Mixing these in one collection silently breaks retrieval (dimension/name mismatch).
- Ollama MCP SSE endpoint is /message (not /sse) and binds a random port at startup; the port is printed to stderr by [MCP/ollama-mcp/src/index.ts](MCP/ollama-mcp/src/index.ts). For HTTP bridge docs see [MCP/ollama-bridge/server.js](MCP/ollama-bridge/server.js).
- Quick single-test and smoke references to cite in answers (non-standard paths):
  - Single pytest (run from MCP/mcp-server-qdrant): `uv run -m pytest tests/test_qdrant_integration.py::test_store_and_search -q`
  - In-memory inspector (no external Qdrant): `QDRANT_URL=":memory:" COLLECTION_NAME="test" fastmcp dev src/mcp_server_qdrant/server.py`
- When documenting “how to run”:
  - For remote SSE/Docker mention `FASTMCP_HOST=0.0.0.0` (binding to localhost is the common failure) — see [MCP/mcp-server-qdrant/README.md](MCP/mcp-server-qdrant/README.md).
  - Note fixed payload keys used by tools: `"document"` and `"metadata"` — defined in [MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py](MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py) and used in [MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py](MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py).
- Prefer citing repo-specific command snippets from [CLAUDE.md](CLAUDE.md) (e.g., uvx, inspector, docker blocks) rather than generic npm/pip commands.