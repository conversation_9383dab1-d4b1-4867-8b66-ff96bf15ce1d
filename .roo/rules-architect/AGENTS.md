# AGENTS.md

This file provides guidance to agents when working with code in this repository.

# Project Architecture Rules (Non-Obvious Only)

- Configuration boundary: Only transport is CLI; all other config is ENV-only. The entrypoint parses `--transport` and defers server construction so env is read at import time: see [main()](MCP/mcp-server-qdrant/src/mcp_server_qdrant/main.py:14) and [server.py](MCP/mcp-server-qdrant/src/mcp_server_qdrant/server.py). Design implication: change env, then import/run; flags won’t override settings.
- Settings validation is delayed until [QdrantSettings](MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py:74) is instantiated. If both QDRANT_LOCAL_PATH and QDRANT_URL/API_KEY are present, validation raises after construction, not at process start. Architect flows to surface misconfig early if wrapping.
- Vector naming contract: The named vector used by Qdrant is derived from the embedding provider; both collection creation and queries must use the same name. Creation path: [create_collection using vector_name](MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py:151). Query path requires `using=vector_name`: [query_points using](MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py:124). Changing provider/model requires collection recreation.
- Fixed payload schema: content and metadata keys are hard-coded and reused across write/read paths. Constant: [METADATA_PATH](MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py:18). Upsert packs payload as `{"document": text, "metadata": ...}`: [upsert payload](MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py:81). Altering keys breaks search and tests.
- Two embedding stacks by design (do not cross): 
  - MCP server uses FastEmbed via provider factory (only FASTEMBED supported): [create_embedding_provider() switch](MCP/mcp-server-qdrant/src/mcp_server_qdrant/embeddings/factory.py:12). Default model MiniLM‑L6‑v2 (384 dims).
  - Top-level embedding tools use Ollama Qwen (4096/2560/1024 dims). Architectural rule: never share a collection between these stacks; dimensions and vector names differ and lead to empty results or errors.
- Collection lifecycle: Collections are lazily created on first write with COSINE distance and provider-sized vector config: [ensure_collection_exists()](MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py:140). Searching a non-existent collection returns empty instead of raising.
- Indexing strategy: Optional payload indexing is driven at construction time via field index map; indexes are created only when the collection is first created: [payload index creation](MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py:164). Combine with [filterable_fields](MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py:88) to expose safe filters; arbitrary filters are gated by env.
- Transports and visibility: Server supports stdio, sse, and streamable-http via FastMCP, but only transport is runtime-selected. For remote/SSE and container use, bind host explicitly with FASTMCP_HOST=0.0.0.0 (see [README.md](MCP/mcp-server-qdrant/README.md)). Ollama MCP exposes SSE on /message and binds a random port; the port is printed on startup stderr: [index.ts SSE setup](MCP/ollama-mcp/src/index.ts:549), [port log](MCP/ollama-mcp/src/index.ts:559).
- Failure modes to account for in architecture:
  - First-run FastEmbed model download stalls tests/network: see [TestFastEmbedProviderIntegration](MCP/mcp-server-qdrant/tests/test_fastembed_integration.py:8). Pre-warm or retry.
  - Mixed-model collections silently degrade to empty results due to `using` mismatch; enforce per-model isolation at the boundary layer.
  - Misconfig shows late; add early construction of [QdrantSettings](MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py:74) and log derived config before server.run in orchestrators.