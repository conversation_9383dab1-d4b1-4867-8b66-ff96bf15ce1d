# AGENTS.md

This file provides guidance to agents when working with code in this repository.

# Project Debug Rules (Non-Obvious Only)

- Qdrant MCP config is ENV-only; only CLI flag is --transport. Misconfig is silent until settings are constructed in [QdrantSettings](MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py:74). If both QDRANT_LOCAL_PATH and QDRANT_URL/API_KEY are set, settings validation raises.
- Use in-memory isolation to rule out Qdrant connectivity/config: `QDRANT_URL=":memory:" COLLECTION_NAME="test" fastmcp dev src/mcp_server_qdrant/server.py` (see [main()](MCP/mcp-server-qdrant/src/mcp_server_qdrant/main.py:4), tests: [test_store_and_search()](MCP/mcp-server-qdrant/tests/test_qdrant_integration.py:33)).
- Dimension/name mismatches manifest as query errors or empty results. Collections are created with a named vector derived from the embedding provider/model; searches must use that exact name (using=…). Creation and search paths: [create_collection vector_name](MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py:151) and [query_points using=vector_name](MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py:124). Fix by recreating the collection after model/provider changes.
- FastEmbed model downloads on first use during tests; failures look like network timeouts on the first pytest run. Re-run after connectivity, or pre-warm by running the FastEmbed tests: [TestFastEmbedProviderIntegration](MCP/mcp-server-qdrant/tests/test_fastembed_integration.py:8).
- Increase verbosity for FastMCP server runs with env: FASTMCP_DEBUG=1 and FASTMCP_LOG_LEVEL=DEBUG (documented in [README.md](MCP/mcp-server-qdrant/README.md)). Helpful when debugging SSE transport startup.
- Ollama MCP SSE endpoint is /message (not /sse) and the HTTP server binds to a dynamic port printed at startup (stderr). See SSE setup in [index.ts](MCP/ollama-mcp/src/index.ts:549) and the port log in [index.ts](MCP/ollama-mcp/src/index.ts:559).
- Docker/remote SSE visibility: Qdrant MCP defaults to 127.0.0.1; set FASTMCP_HOST=0.0.0.0 when containerized (see [README.md](MCP/mcp-server-qdrant/README.md)). Missing host binding looks like connection timeouts from remote clients.
- Payload shape is fixed: content under payload["document"] and metadata under payload["metadata"] (constant defined in [settings.py METADATA_PATH](MCP/mcp-server-qdrant/src/mcp_server_qdrant/settings.py:18) and used in [upsert payload](MCP/mcp-server-qdrant/src/mcp_server_qdrant/qdrant.py:81)). Changing keys will break retrieval.
- Quick health checks:
  - Qdrant: `curl -sS http://localhost:6333/healthz`
  - In-memory smoke: `QDRANT_URL=":memory:" COLLECTION_NAME="test" fastmcp dev src/mcp_server_qdrant/server.py`
  - Single failing test reproduction: `uv run -m pytest tests/test_qdrant_integration.py::test_store_and_search -q` (run from MCP/mcp-server-qdrant)
- Lint/type checks that commonly catch regressions before runtime:
  - Ruff / ruff-format / isort: [ .pre-commit-config.yaml](MCP/mcp-server-qdrant/.pre-commit-config.yaml)
  - Type checkers: `uv run pyright` (pyproject dev deps: [pyproject.toml](MCP/mcp-server-qdrant/pyproject.toml))