# AGENTS.md

This file provides guidance to agents when working with code in this repository.

# Project Coding Rules (Non-Obvious Only)

- Qdrant MCP server is ENV-only; the only supported CLI flag is --transport. All config must come from env (QDRANT_URL, QDRANT_LOCAL_PATH, QDRANT_API_KEY, COLLEC<PERSON>ON_NAME, EMBEDDING_MODEL, etc.). See entrypoint and settings.
- QDRANT_LOCAL_PATH is mutually exclusive with QDRANT_URL and QDRANT_API_KEY; combining them raises at settings validation time.
- Payload schema is fixed: content is stored under payload["document"] and metadata under payload["metadata"]. Keep these keys stable to avoid search/deserialization issues.
- The named vector (“using”) is derived from the embedding provider; queries must pass using=provider_vector_name. If you change embedding model/provider, you must recreate the collection (dimension and vector name change).
- FastEmbed default model for the MCP server is sentence-transformers/all-MiniLM-L6-v2 (384 dims); top-level embedding scripts use <PERSON>wen (4096/2560/1024 dims). Never write/search across these stacks in the same collection.
- Collections are auto-created on first write with COSINE distance and provider-sized vectors; searching a non-existent collection returns empty (not an error).
- Filterable fields: define via QdrantSettings.filterable_fields to auto-index payload keys; fields with condition=None are indexed but not exposed as MCP tool filters; arbitrary filters are OFF unless QDRANT_ALLOW_ARBITRARY_FILTER=true.
- Python style/tooling: ruff + ruff-format; isort --profile black; type-check with both mypy and pyright; target Python ≥3.10. Keep annotations complete—tests are asyncio-aware.
- Node/TS (Ollama MCP): supports stdio and SSE on /message (not /sse); HTTP server listens on a random port (listen(0)) and prints the port on startup; TS is strict with Node16 module/resolution and ES2022 target; streaming uses Web Streams (TransformStream) at runtime (Node ≥18).
- Ollama bridge (server.js) is a minimal HTTP facade on :3000 with POST /complete, GET /models, GET /health and a hard-coded OLLAMA_HOST (edit code to change).
- Commands (only non-defaults shown):
  - Run Qdrant MCP over SSE:
    QDRANT_URL="http://localhost:6333" COLLECTION_NAME="dev" FASTMCP_PORT=8000 uvx mcp-server-qdrant --transport sse
  - Inspector (in‑memory):
    QDRANT_URL=":memory:" COLLECTION_NAME="test" fastmcp dev src/mcp_server_qdrant/server.py
  - Single Python test (from MCP/mcp-server-qdrant):
    uv run -m pytest tests/test_qdrant_integration.py::test_store_and_search -q
  - Lint+format:
    uv run pre-commit run --all-files
  - Type check:
    uv run pyright
  - Build/run Ollama MCP:
    cd MCP/ollama-mcp && npm install && npm run build && npm run inspector
  - Start Ollama bridge:
    cd MCP/ollama-bridge && npm install && npm start