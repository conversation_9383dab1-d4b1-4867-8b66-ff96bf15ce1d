#!/bin/bash

# YendorCats Embedding Runner Script
# ==================================
# This script sets up the Python environment and runs the embedding process
# for your YendorCats project content into a local Qdrant vector database.

set -e

echo "🐱 YendorCats Content Embedding Setup"
echo "====================================="

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo "❌ uv is not installed. Please install it first:"
    echo "curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

echo "✅ uv is available"

# Check if Qdrant is running on localhost:6333
echo "🔍 Checking if Qdrant is running on localhost:6333..."
if curl -s -f http://localhost:6333/collections > /dev/null 2>&1; then
    echo "✅ Qdrant is running on localhost:6333"
else
    echo "❌ Qdrant is not running on localhost:6333"
    echo ""
    echo "Please start Qdrant first. You can use Docker:"
    echo "docker run -p 6333:6333 qdrant/qdrant"
    echo ""
    echo "Or install and run Qdrant locally:"
    echo "https://qdrant.tech/documentation/guides/installation/"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "🔧 Creating Python virtual environment..."
    uv venv
fi

echo "🔧 Activating virtual environment..."
source venv/bin/activate

echo "📦 Installing Python dependencies..."
uv pip install -r requirements.txt

echo "🧠 Starting embedding process..."
echo "⚠️  This will take a long time as mentioned!"
echo ""
echo "The script will:"
echo "  1. Process all documentation and code files"
echo "  2. Create text chunks for optimal embedding"
echo "  3. Generate embeddings using OpenAI text-embedding-3-large"
echo "  4. Store embeddings in your local Qdrant instance"
echo ""
echo "Progress will be shown as files are processed..."
echo ""

# Run the embedding script
python embed_content.py

echo ""
echo "🎉 Embedding process completed!"
echo ""
echo "Your Qdrant collection 'yendorcats_docs' is now ready for semantic search."
echo "You can query it using the Qdrant API or your MCP server."
