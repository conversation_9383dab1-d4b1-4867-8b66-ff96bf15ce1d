# S3 Metadata Guide for YendorCats Gallery

This document outlines the custom S3 metadata structure used by the YendorCats application to display gallery images correctly. The application's `MetadataSyncService` prioritizes these S3 metadata fields over any other data source.

## Metadata Structure

The following metadata keys are recognized by the application. All keys are case-insensitive.

### **`cat-name`** (Required)
The name of the cat. This is a primary identifier.
- **<PERSON> checked:** `cat-name`, `name`, `catname`
- **Example:** `Loretta`

### **`age`** (Optional)
The age of the cat at the time the photo was taken, specified in months.
- **Keys checked:** `age`, `cat-age`, `age-at-photo`
- **Format:** A string containing an integer (e.g., "13"). The application can also parse descriptive strings like "6 months" or "2 years".
- **Example:** `13`

### **`breed`** (Optional)
The breed of the cat.
- **Keys checked:** `breed`, `cat-breed`
- **Default:** "Maine Coon" if not specified.
- **Example:** `Maine Coon`

### **`gender`** (Optional)
The gender of the cat.
- **Keys checked:** `gender`, `sex`
- **Format:** `M` for Male, `F` for Female, `U` for Unknown.
- **Example:** `F`

### **`description`** (Optional)
A short description of the cat or the photo.
- **Keys checked:** `description`, `desc`
- **Example:** `Beautiful Maine Coon queen cat with stunning features`

### **`tags`** (Optional)
Comma-separated tags for filtering and organization.
- **Keys checked:** `tags`, `keywords`
- **Example:** `maine-coon,queen,breeding,loretta`

### **`date-taken`** (Optional)
The date the photograph was taken.
- **Keys checked:** `date-taken`, `photo-date`, `taken-date`
- **Format:** `YYYY-MM-DD`
- **Example:** `2024-08-03`

### **`category`** (Required)
The gallery category the image belongs to.
- **Keys checked:** `category`
- **Values:** `queens`, `studs`, `kittens`, `gallery`
- **Example:** `queens`

### **`featured`** (Required for Carousel)
Determines if the image should be displayed in the main carousel.
- **Key:** `featured`
- **Values:** `true` or `false`
- **Example:** `true`

### **`active`** (Required)
Determines if the image is active and should be displayed in the gallery.
- **Key:** `active`
- **Values:** `true` or `false`
- **Example:** `true`

## Utility Script

A Python script, [`set_s3_metadata.py`](../set_s3_metadata.py:1), is available in the root directory to simplify the process of setting this metadata on an S3 object. It uses the credentials from the `.env` file.
