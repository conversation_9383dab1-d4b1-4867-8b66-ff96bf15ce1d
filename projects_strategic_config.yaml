projects:
  # TIER 1: Universal Projects Index - All projects for broad context
  - name: "Universal Projects Index"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects"
    type: "universal"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-0.6B:Q8_0"  # Fast model for broad coverage
    priority: "high"
    exclude_patterns:
      - "node_modules"
      - ".git"
      - "__pycache__"
      - "*.pyc"
      - ".venv"
      - ".env"
      - "dist"
      - "build"
      - ".DS_Store"
      - "*.log"
      - ".terraform"
      - "*.tfstate"
      - "backup*"
      - "*.bak"
    include_patterns:
      - "*.md"
      - "*.txt"
      - "*.py"
      - "*.js"
      - "*.ts"
      - "*.json"
      - "*.yaml"
      - "*.yml"
      - "README*"
      - "package.json"
      - "requirements.txt"
      - "*.tf"
      - "Dockerfile"
      - "docker-compose.yml"
    tags: ["universal", "projects-overview", "context"]

  # TIER 2: DevOps & Infrastructure - High quality embeddings
  - name: "DevOps & Infrastructure"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects"
    type: "devops"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-8B:Q4_K_M"  # High quality for critical infra
    priority: "high"
    exclude_patterns:
      - "node_modules"
      - ".git"
      - "__pycache__"
      - "dist"
      - "build"
      - ".DS_Store"
    include_patterns:
      - "hardening/**/*"
      - "paceyspace-cluster/**/*"
      - "MCP/**/*"
      - "**/docker-compose.yml"
      - "**/Dockerfile"
      - "**/*.tf"
      - "**/*.yml"
      - "**/*.yaml"
      - "**/README.md"
      - "**/setup*.sh"
      - "**/install*.sh"
      - "**/configure*.sh"
    tags: ["devops", "infrastructure", "kubernetes", "terraform", "docker"]

  # TIER 3: Web Applications - Balanced quality
  - name: "Web Applications"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects"
    type: "web"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-4B:Q4_K_M"  # Balanced for web apps
    priority: "high"
    exclude_patterns:
      - "node_modules"
      - "dist"
      - "build"
      - ".next"
      - ".nuxt"
      - ".git"
      - "__pycache__"
      - ".DS_Store"
    include_patterns:
      - "cabreguca-website/**/*"
      - "yendorcats/**/*"
      - "yendorcats-wp/**/*"
      - "open_web_ui-ollama/**/*"
      - "paceyspace-branding/**/*"
      - "**/package.json"
      - "**/*.js"
      - "**/*.ts"
      - "**/*.tsx"
      - "**/*.jsx"
      - "**/*.vue"
      - "**/*.css"
      - "**/*.scss"
      - "**/README.md"
      - "**/*.md"
    tags: ["web", "frontend", "react", "wordpress", "ui"]
    description: "Balanced quality embeddings for web applications and frontend projects"

  # TIER 4: AI & Machine Learning - High quality
  - name: "AI & Machine Learning"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects"
    type: "ai"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-8B:Q4_K_M"  # High quality for AI work
    priority: "medium"
    exclude_patterns:
      - "node_modules"
      - ".git"
      - "__pycache__"
      - "*.pyc"
      - ".venv"
      - "models/"
      - "datasets/"
      - ".DS_Store"
    include_patterns:
      - "ollama/**/*"
      - "**/requirements.txt"
      - "**/*.py"
      - "**/*.ipynb"
      - "**/README.md"
      - "**/*.md"
      - "**/model*.py"
      - "**/train*.py"
      - "**/inference*.py"
    tags: ["ai", "machine-learning", "ollama", "models"]
    description: "High-quality embeddings for AI, ML, and LLM-related projects"

  # TIER 5: Development Tools & Utilities - Fast processing
  - name: "Development Tools"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects"
    type: "tools"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-0.6B:Q8_0"  # Fast for utilities
    priority: "low"
    exclude_patterns:
      - "node_modules"
      - ".git"
      - "__pycache__"
      - "dist"
      - "build"
      - ".DS_Store"
    include_patterns:
      - "enhanceCP/**/*"
      - "NoobSlayer_Kernal/**/*"
      - "yendor/**/*"
      - "yendorstatic/**/*"
      - "Intro to HTML/**/*"
      - "**/README.md"
      - "**/*.py"
      - "**/*.js"
      - "**/*.sh"
    tags: ["tools", "utilities", "development", "scripts"]
    description: "Fast processing for development tools and utility projects"

routing_strategy:
  # Collection routing based on project type and content
  universal:
    collection: "universal_projects_0_6b"
    description: "Fast universal index - all projects get embedded here for broad context"

  devops:
    collection: "shared_knowledge_8b"  # Use existing high-quality shared collection
    description: "Critical infrastructure knowledge in high-quality collection"

  web:
    collection: "paceyspace_projects_8b"  # Use existing project collection
    description: "Web applications in dedicated project collection"

  ai:
    collection: "shared_knowledge_8b"  # High-quality AI knowledge
    description: "AI/ML projects in high-quality knowledge base"

  tools:
    collection: "shared_docs_4b"  # General documentation collection
    description: "Development tools in general documentation collection"

collection_strategy:
  description: "Multi-tier embedding strategy for uncategorized projects folder"

  tiers:
    tier_1_universal:
      purpose: "Broad contextual awareness across all projects"
      model: "0.6B (fast)"
      coverage: "All projects"
      use_case: "Quick project discovery and general context"

    tier_2_critical:
      purpose: "High-quality embeddings for critical systems"
      model: "8B (high quality)"
      coverage: "DevOps, AI, Infrastructure"
      use_case: "Detailed technical assistance and troubleshooting"

    tier_3_balanced:
      purpose: "Balanced quality for regular development work"
      model: "4B (balanced)"
      coverage: "Web apps, larger codebases"
      use_case: "Feature development and code assistance"

    tier_4_utilities:
      purpose: "Fast processing for supporting tools"
      model: "0.6B (fast)"
      coverage: "Scripts, utilities, small tools"
      use_case: "Quick reference and simple automation"

processing_order:
  # Process in order of strategic importance
  1: "Universal Projects Index"    # Get broad context first
  2: "DevOps & Infrastructure"     # Critical systems
  3: "Web Applications"            # Main development work
  4: "AI & Machine Learning"       # Specialized knowledge
  5: "Development Tools"           # Supporting utilities

benefits:
  - "Broad context awareness across all projects"
  - "Strategic allocation of compute resources"
  - "High-quality embeddings where they matter most"
  - "Fast universal search capabilities"
  - "Efficient storage and retrieval"
  - "Scalable to new project additions"