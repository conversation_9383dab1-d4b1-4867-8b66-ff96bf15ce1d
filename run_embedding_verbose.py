#!/usr/bin/env python3
"""
Verbose embedding script for MCP directory using Qwen3-8B with real-time progress
"""
import sys
import os
import time
from pathlib import Path
from embed_documents import DocumentEmbedder

def main():
    print("🚀 Running MCP Directory Embedding with Qwen3-8B (4096 dimensions)")
    print("=" * 60)

    # Initialize embedder
    qdrant_url = os.getenv('QDRANT_URL', 'http://localhost:6333')
    print(f"📡 Connecting to Qdrant at: {qdrant_url}")

    embedder = DocumentEmbedder(qdrant_url)

    # Configuration
    agent = "claude"
    model = "dengcao/Qwen3-Embedding-8B:Q4_K_M"
    path = Path("/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP/MCP")
    shared = True

    print(f"\n📊 Configuration:")
    print(f"  Agent: {agent}")
    print(f"  Model: {model}")
    print(f"  Path: {path}")
    print(f"  Shared collections: {shared}")

    # Test Ollama connection
    print(f"\n🔍 Testing Ollama connection...")
    try:
        test_embedding = embedder.create_embedding("test", model)
        if test_embedding:
            print(f"✅ Ollama connection successful! Embedding dimensions: {len(test_embedding)}")
        else:
            print("❌ Failed to create test embedding")
            return
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return

    # Show current collections before
    print(f"\n📚 Current collections:")
    collections = embedder.list_collections()
    for col in sorted(collections, key=lambda x: x['name']):
        print(f"  - {col['name']}: {col['points_count']} points")

    # Get list of files to process
    print(f"\n🔍 Scanning directory: {path}")
    extensions = ['.txt', '.md', '.py', '.js', '.ts', '.json', '.yaml', '.yml', '.cpp', '.c', '.h']
    files_to_process = []

    for file_path in path.rglob('*'):
        if file_path.is_file() and file_path.suffix.lower() in extensions:
            files_to_process.append(file_path)

    print(f"📁 Found {len(files_to_process)} files to process")

    # Process files with progress
    files_processed = 0
    files_success = 0
    start_time = time.time()

    for i, file_path in enumerate(files_to_process, 1):
        print(f"\n[{i}/{len(files_to_process)}] Processing: {file_path.name}")
        print(f"  📂 Path: {file_path.relative_to(path)}")

        try:
            # Show file size
            file_size = file_path.stat().st_size
            print(f"  📏 Size: {file_size:,} bytes")

            # Process the file
            success = embedder.embed_file(file_path, agent, model, shared)
            files_processed += 1

            if success:
                files_success += 1
                print(f"  ✅ Successfully embedded")
            else:
                print(f"  ❌ Failed to embed")

        except Exception as e:
            print(f"  💥 Error: {e}")
            files_processed += 1

        # Show progress stats
        elapsed = time.time() - start_time
        if files_processed > 0:
            avg_time = elapsed / files_processed
            remaining = len(files_to_process) - files_processed
            eta = remaining * avg_time
            print(f"  ⏱️  Progress: {files_success}/{files_processed} successful, ETA: {eta/60:.1f}m")

    # Show final results
    total_time = time.time() - start_time
    print(f"\n🎉 Embedding Complete!")
    print(f"✅ Processed {files_success}/{files_processed} files successfully")
    print(f"⏱️  Total time: {total_time/60:.1f} minutes")
    print(f"📈 Average: {total_time/files_processed:.1f} seconds per file")

    # Show final collections state
    print(f"\n📊 Final collection state:")
    collections = embedder.list_collections()
    for col in sorted(collections, key=lambda x: x['name']):
        print(f"  {col['name']}: {col['points_count']} points")

if __name__ == "__main__":
    main()