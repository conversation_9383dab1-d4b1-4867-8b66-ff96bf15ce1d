#!/usr/bin/env python3
"""
Shared Context Management System for Multi-Agent AI

This system enables stable, consistent context sharing across multiple AI agents
(<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, etc.) using Qdrant vector database.

Key Features:
- Cross-agent knowledge retrieval
- Context consistency management
- Knowledge evolution tracking
- Conflict resolution for overlapping information
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
import json

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import (
        Distance, VectorParams, PointStruct, Filter, FieldCondition,
        MatchValue, SearchRequest, SearchParams
    )
    import ollama
except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Install with: pip install qdrant-client ollama")
    sys.exit(1)

from qdrant_strategy import QdrantMultiTenantStrategy, ContentType


@dataclass
class ContextSearchResult:
    """Result from context search across multiple agents"""
    content: str
    agent: str
    model: str
    collection: str
    score: float
    timestamp: str
    file_path: Optional[str] = None
    content_type: Optional[str] = None


@dataclass
class KnowledgeConflict:
    """Represents conflicting information between agents"""
    topic: str
    conflicts: List[ContextSearchResult]
    resolution: Optional[str] = None


class SharedContextManager:
    """
    Manages shared context across multiple AI agents with consistency guarantees
    """

    def __init__(self, qdrant_url: str = "http://localhost:6333", api_key: Optional[str] = None):
        self.client = QdrantClient(url=qdrant_url, api_key=api_key)
        self.ollama_client = ollama.Client()
        self.strategy = QdrantMultiTenantStrategy()

    def search_cross_agent_context(
        self,
        query: str,
        requesting_agent: str,
        content_type: ContentType = ContentType.DOCS,
        limit: int = 10,
        include_private: bool = False
    ) -> List[ContextSearchResult]:
        """
        Search for context across multiple agent collections.

        Args:
            query: Search query
            requesting_agent: Agent making the request
            content_type: Type of content to search
            limit: Maximum results to return
            include_private: Whether to include agent-specific collections

        Returns:
            List of context search results ranked by relevance
        """
        # Get collections to search
        if include_private:
            collections = self._get_all_accessible_collections(requesting_agent, content_type)
        else:
            collections = self.strategy.get_cross_agent_search_collections(requesting_agent, content_type)

        # Create embeddings for query using multiple models for better coverage
        query_embeddings = self._create_query_embeddings(query)

        all_results = []

        for collection_name in collections:
            try:
                # Check if collection exists
                if not self._collection_exists(collection_name):
                    continue

                # Get collection info to determine expected vector size
                collection_info = self.client.get_collection(collection_name)
                expected_size = collection_info.config.params.vectors.size

                # Find compatible embedding model for this collection
                compatible_model = None
                compatible_embedding = None

                for model, embedding in query_embeddings.items():
                    if not embedding:
                        continue
                    if len(embedding) == expected_size:
                        compatible_model = model
                        compatible_embedding = embedding
                        break

                if not compatible_embedding:
                    continue  # Skip this collection if no compatible model found

                search_results = self.client.search(
                    collection_name=collection_name,
                    query_vector=compatible_embedding,
                    limit=limit,
                    with_payload=True
                )

                # Convert to ContextSearchResult
                for result in search_results:
                    if result.payload:
                        all_results.append(ContextSearchResult(
                            content=result.payload.get('content', ''),
                            agent=result.payload.get('agent', 'unknown'),
                            model=result.payload.get('model', compatible_model),
                            collection=collection_name,
                            score=result.score,
                            timestamp=result.payload.get('timestamp', ''),
                            file_path=result.payload.get('file_path'),
                            content_type=result.payload.get('content_type')
                        ))

            except Exception as e:
                print(f"Error searching collection {collection_name}: {e}")
                continue

        # Sort by score and remove duplicates
        all_results.sort(key=lambda x: x.score, reverse=True)
        return self._deduplicate_results(all_results)[:limit]

    def find_knowledge_conflicts(
        self,
        topic: str,
        agents: Optional[List[str]] = None,
        threshold: float = 0.7
    ) -> List[KnowledgeConflict]:
        """
        Find conflicting information about a topic across different agents.

        Args:
            topic: Topic to search for conflicts
            agents: List of agents to check (None for all)
            threshold: Similarity threshold for detecting conflicts

        Returns:
            List of detected knowledge conflicts
        """
        if agents is None:
            agents = list(self.strategy.agents.keys())

        # Search for the topic across all agents
        all_results = []
        for agent in agents:
            results = self.search_cross_agent_context(
                query=topic,
                requesting_agent=agent,
                include_private=True,
                limit=5
            )
            all_results.extend(results)

        # Group results by similarity and detect conflicts
        conflicts = []
        processed_results = set()

        for i, result1 in enumerate(all_results):
            if i in processed_results:
                continue

            similar_results = [result1]
            processed_results.add(i)

            # Find similar results from other agents
            for j, result2 in enumerate(all_results[i+1:], i+1):
                if j in processed_results:
                    continue

                if (result1.agent != result2.agent and
                    self._are_results_similar(result1, result2, threshold)):
                    similar_results.append(result2)
                    processed_results.add(j)

            # Check if we have conflicting information
            if len(similar_results) > 1 and self._detect_content_conflict(similar_results):
                conflicts.append(KnowledgeConflict(
                    topic=topic,
                    conflicts=similar_results
                ))

        return conflicts

    def consolidate_knowledge(
        self,
        topic: str,
        target_agent: str = "shared",
        resolution_strategy: str = "latest"
    ) -> bool:
        """
        Consolidate conflicting knowledge about a topic into shared collection.

        Args:
            topic: Topic to consolidate
            target_agent: Target agent for consolidated knowledge
            resolution_strategy: How to resolve conflicts ("latest", "highest_score", "manual")

        Returns:
            True if consolidation was successful
        """
        # Find conflicts for this topic
        conflicts = self.find_knowledge_conflicts(topic)

        if not conflicts:
            print(f"No conflicts found for topic: {topic}")
            return True

        print(f"Found {len(conflicts)} conflicts for topic: {topic}")

        # Resolve each conflict
        for conflict in conflicts:
            resolved_content = self._resolve_conflict(conflict, resolution_strategy)

            if resolved_content:
                # Store resolved content in shared collection
                success = self._store_consolidated_knowledge(
                    topic=topic,
                    content=resolved_content,
                    source_results=conflict.conflicts,
                    target_agent=target_agent
                )

                if success:
                    print(f"✅ Consolidated knowledge for: {topic}")
                else:
                    print(f"❌ Failed to consolidate knowledge for: {topic}")

        return True

    def get_agent_knowledge_stats(self) -> Dict[str, Dict]:
        """
        Get statistics about knowledge distribution across agents.

        Returns:
            Dictionary with agent knowledge statistics
        """
        stats = {}

        try:
            collections = self.client.get_collections().collections

            for collection in collections:
                collection_info = self.client.get_collection(collection.name)

                # Extract agent from collection name
                for agent in self.strategy.agents.keys():
                    if agent in collection.name or collection.name.startswith("shared"):
                        agent_key = agent if agent in collection.name else "shared"

                        if agent_key not in stats:
                            stats[agent_key] = {
                                "collections": [],
                                "total_points": 0,
                                "content_types": set(),
                                "models_used": set()
                            }

                        stats[agent_key]["collections"].append(collection.name)
                        stats[agent_key]["total_points"] += collection_info.points_count

                        # Get sample points to analyze content types and models
                        sample_points = self.client.scroll(
                            collection_name=collection.name,
                            limit=10,
                            with_payload=True
                        )

                        for point, _ in sample_points:
                            if point.payload:
                                stats[agent_key]["content_types"].add(
                                    point.payload.get("content_type", "unknown")
                                )
                                stats[agent_key]["models_used"].add(
                                    point.payload.get("model", "unknown")
                                )

        except Exception as e:
            print(f"Error getting agent statistics: {e}")

        # Convert sets to lists for JSON serialization
        for agent_stats in stats.values():
            agent_stats["content_types"] = list(agent_stats["content_types"])
            agent_stats["models_used"] = list(agent_stats["models_used"])

        return stats

    def sync_agent_knowledge(
        self,
        source_agent: str,
        target_agent: str,
        content_type: ContentType = ContentType.KNOWLEDGE,
        min_score: float = 0.8
    ) -> int:
        """
        Sync high-quality knowledge from one agent to another.

        Args:
            source_agent: Agent to copy knowledge from
            target_agent: Agent to copy knowledge to
            content_type: Type of content to sync
            min_score: Minimum quality score for syncing

        Returns:
            Number of knowledge items synced
        """
        # Get source collections
        source_collections = [
            col for col in self.strategy.get_cross_agent_search_collections(source_agent, content_type)
            if source_agent in col
        ]

        synced_count = 0

        for collection_name in source_collections:
            try:
                # Get all points from source collection
                points, _ = self.client.scroll(
                    collection_name=collection_name,
                    limit=1000,  # Adjust based on needs
                    with_payload=True
                )

                # Filter high-quality content
                high_quality_points = [
                    point for point in points
                    if (point.payload and
                        point.payload.get('file_size', 0) > 100 and  # Minimum content size
                        point.payload.get('content_type') == content_type.value)
                ]

                if not high_quality_points:
                    continue

                # Get target collection
                target_collection = self.strategy.get_collection_for_context(
                    target_agent, content_type,
                    high_quality_points[0].payload.get('model'),
                    shared=True
                )

                # Copy points to target collection
                new_points = []
                for point in high_quality_points:
                    # Update payload to reflect new agent
                    new_payload = point.payload.copy()
                    new_payload['synced_from'] = source_agent
                    new_payload['synced_to'] = target_agent
                    new_payload['sync_timestamp'] = datetime.now().isoformat()

                    new_point = PointStruct(
                        id=hash(f"sync_{point.id}_{target_agent}") & 0x7FFFFFFFFFFFFFFF,
                        vector=point.vector,
                        payload=new_payload
                    )
                    new_points.append(new_point)

                if new_points:
                    self.client.upsert(collection_name=target_collection, points=new_points)
                    synced_count += len(new_points)

            except Exception as e:
                print(f"Error syncing from collection {collection_name}: {e}")
                continue

        return synced_count

    # Private helper methods

    def _create_query_embeddings(self, query: str) -> Dict[str, List[float]]:
        """Create embeddings for query using available models"""
        embeddings = {}

        for model_name in self.strategy.embedding_models.keys():
            try:
                response = self.ollama_client.embeddings(model=model_name, prompt=query)
                embeddings[model_name] = response['embedding']
            except Exception as e:
                print(f"Error creating embedding with {model_name}: {e}")
                embeddings[model_name] = []

        return embeddings

    def _collection_exists(self, collection_name: str) -> bool:
        """Check if collection exists"""
        try:
            collections = self.client.get_collections().collections
            return any(col.name == collection_name for col in collections)
        except:
            return False

    def _get_all_accessible_collections(self, agent: str, content_type: ContentType) -> List[str]:
        """Get all collections accessible to an agent"""
        collections = self.strategy.get_cross_agent_search_collections(agent, content_type)

        # Add agent-specific collections
        try:
            all_collections = self.client.get_collections().collections
            for col in all_collections:
                if agent in col.name and col.name not in collections:
                    collections.append(col.name)
        except:
            pass

        return collections

    def _deduplicate_results(self, results: List[ContextSearchResult]) -> List[ContextSearchResult]:
        """Remove duplicate results based on content similarity"""
        unique_results = []
        seen_content = set()

        for result in results:
            # Use content hash to identify duplicates
            content_hash = hash(result.content.strip().lower())
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_results.append(result)

        return unique_results

    def _are_results_similar(self, result1: ContextSearchResult, result2: ContextSearchResult, threshold: float) -> bool:
        """Check if two results are similar enough to be considered related"""
        # Simple similarity check based on content overlap
        words1 = set(result1.content.lower().split())
        words2 = set(result2.content.lower().split())

        if not words1 or not words2:
            return False

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        similarity = intersection / union if union > 0 else 0
        return similarity >= threshold

    def _detect_content_conflict(self, results: List[ContextSearchResult]) -> bool:
        """Detect if results contain conflicting information"""
        # Simple conflict detection - check for contradictory keywords
        conflict_keywords = [
            ('true', 'false'), ('yes', 'no'), ('correct', 'incorrect'),
            ('valid', 'invalid'), ('working', 'broken'), ('enabled', 'disabled')
        ]

        contents = [result.content.lower() for result in results]

        for positive, negative in conflict_keywords:
            has_positive = any(positive in content for content in contents)
            has_negative = any(negative in content for content in contents)

            if has_positive and has_negative:
                return True

        return False

    def _resolve_conflict(self, conflict: KnowledgeConflict, strategy: str) -> Optional[str]:
        """Resolve a knowledge conflict using specified strategy"""
        if not conflict.conflicts:
            return None

        if strategy == "latest":
            # Use the most recent information
            latest_result = max(conflict.conflicts, key=lambda x: x.timestamp)
            return latest_result.content

        elif strategy == "highest_score":
            # Use the highest scoring result
            best_result = max(conflict.conflicts, key=lambda x: x.score)
            return best_result.content

        elif strategy == "manual":
            # Present options for manual resolution
            print(f"\nConflict found for topic: {conflict.topic}")
            for i, result in enumerate(conflict.conflicts):
                print(f"{i+1}. {result.agent}: {result.content[:100]}...")

            choice = input("Select the correct version (number): ").strip()
            try:
                index = int(choice) - 1
                if 0 <= index < len(conflict.conflicts):
                    return conflict.conflicts[index].content
            except ValueError:
                pass

        return None

    def _store_consolidated_knowledge(
        self,
        topic: str,
        content: str,
        source_results: List[ContextSearchResult],
        target_agent: str
    ) -> bool:
        """Store consolidated knowledge in target collection"""
        try:
            # Choose appropriate model (prefer highest quality available)
            model = "dengcao/Qwen3-Embedding-8B:Q4_K_M"
            for result in source_results:
                if "8B" in result.model:
                    model = result.model
                    break

            # Create embedding
            response = self.ollama_client.embeddings(model=model, prompt=content)
            embedding = response['embedding']

            # Get target collection
            collection_name = self.strategy.get_collection_for_context(
                target_agent, ContentType.KNOWLEDGE, model, shared=True
            )

            # Create consolidated point
            point = PointStruct(
                id=hash(f"consolidated_{hash(topic)}_{datetime.now().timestamp()}") & 0x7FFFFFFFFFFFFFFF,
                vector=embedding,
                payload={
                    "agent": target_agent,
                    "model": model,
                    "content": content,
                    "topic": topic,
                    "content_type": ContentType.KNOWLEDGE.value,
                    "consolidated": True,
                    "source_agents": [r.agent for r in source_results],
                    "source_count": len(source_results),
                    "timestamp": datetime.now().isoformat(),
                    "resolution_method": "automated"
                }
            )

            self.client.upsert(collection_name=collection_name, points=[point])
            return True

        except Exception as e:
            print(f"Error storing consolidated knowledge: {e}")
            return False


def main():
    """Main function for context management operations"""
    print("🧠 Shared Context Management System")
    print("="*50)

    # Initialize manager
    qdrant_url = os.getenv('QDRANT_URL', 'http://localhost:6333')
    qdrant_api_key = os.getenv('QDRANT_API_KEY')

    manager = SharedContextManager(qdrant_url, qdrant_api_key)

    # Show available operations
    operations = [
        "search - Search across agent contexts",
        "conflicts - Find knowledge conflicts",
        "consolidate - Consolidate conflicting knowledge",
        "stats - Show agent knowledge statistics",
        "sync - Sync knowledge between agents"
    ]

    print("\n🔧 Available operations:")
    for op in operations:
        print(f"  - {op}")

    operation = input("\nSelect operation: ").strip().lower()

    if operation == "search":
        query = input("Enter search query: ").strip()
        agent = input("Requesting agent (claude/auggie/gemini/qwen): ").strip()

        results = manager.search_cross_agent_context(query, agent)

        print(f"\n🔍 Found {len(results)} results:")
        for result in results:
            print(f"  [{result.agent}] {result.content[:100]}... (score: {result.score:.3f})")

    elif operation == "conflicts":
        topic = input("Enter topic to check for conflicts: ").strip()
        conflicts = manager.find_knowledge_conflicts(topic)

        print(f"\n⚠️  Found {len(conflicts)} conflicts for '{topic}':")
        for conflict in conflicts:
            print(f"  Conflict: {len(conflict.conflicts)} different versions")
            for result in conflict.conflicts:
                print(f"    [{result.agent}] {result.content[:80]}...")

    elif operation == "consolidate":
        topic = input("Enter topic to consolidate: ").strip()
        success = manager.consolidate_knowledge(topic)
        if success:
            print(f"✅ Knowledge consolidation completed for: {topic}")

    elif operation == "stats":
        stats = manager.get_agent_knowledge_stats()
        print(f"\n📊 Agent Knowledge Statistics:")
        for agent, data in stats.items():
            print(f"  {agent}:")
            print(f"    Collections: {len(data['collections'])}")
            print(f"    Total points: {data['total_points']}")
            print(f"    Content types: {', '.join(data['content_types'])}")

    elif operation == "sync":
        source = input("Source agent: ").strip()
        target = input("Target agent: ").strip()
        count = manager.sync_agent_knowledge(source, target)
        print(f"✅ Synced {count} knowledge items from {source} to {target}")


if __name__ == "__main__":
    main()