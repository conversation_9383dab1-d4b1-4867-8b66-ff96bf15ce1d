#!/usr/bin/env python3
"""
Custom embedding script that uses Ollama's Qwen3-Embedding-8B model
to create embeddings for project files and stores them in Qdrant.
"""

import json
import os
import requests
import hashlib
from pathlib import Path
from typing import List, Dict, Any
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct

def get_ollama_embedding(text: str, model: str = "dengcao/Qwen3-Embedding-8B:Q4_K_M") -> List[float]:
    """Get embedding from Ollama server."""
    url = "http://127.0.0.1:11434/api/embeddings"
    payload = {
        "model": model,
        "prompt": text
    }

    response = requests.post(url, json=payload)
    response.raise_for_status()
    return response.json()["embedding"]

def create_collection_if_not_exists(client: QdrantClient, collection_name: str, vector_size: int):
    """Create collection in Qdrant if it doesn't exist."""
    try:
        client.get_collection(collection_name)
        print(f"Collection '{collection_name}' already exists")
    except Exception:
        client.create_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE),
        )
        print(f"Created collection '{collection_name}'")

def get_file_content(file_path: Path) -> str:
    """Read file content, handling different encodings."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin1') as f:
                return f.read()
        except Exception as e:
            print(f"Could not read {file_path}: {e}")
            return ""

def should_embed_file(file_path: Path) -> bool:
    """Check if file should be embedded based on extension and size."""
    # Skip binary files, large files, and common non-source files
    skip_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.zip', '.tar', '.gz', '.exe', '.bin'}
    skip_dirs = {'node_modules', '.git', '__pycache__', '.venv', 'venv', 'env', '.pytest_cache'}

    if file_path.suffix.lower() in skip_extensions:
        return False

    if any(part in skip_dirs for part in file_path.parts):
        return False

    try:
        # Skip files larger than 1MB
        if file_path.stat().st_size > 1024 * 1024:
            return False
    except OSError:
        return False

    return True

def process_project(project_path: str, collection_name: str, client: QdrantClient):
    """Process all files in a project directory."""
    project_path = Path(project_path)
    if not project_path.exists():
        print(f"Project path does not exist: {project_path}")
        return

    print(f"Processing project: {project_path}")

    # Get all relevant files
    files_to_process = []
    for root, dirs, files in os.walk(project_path):
        root_path = Path(root)
        for file_name in files:
            file_path = root_path / file_name
            if should_embed_file(file_path):
                files_to_process.append(file_path)

    print(f"Found {len(files_to_process)} files to embed")

    # Test with first file to get vector size
    if not files_to_process:
        print("No files to process")
        return

    test_content = get_file_content(files_to_process[0])[:1000]  # First 1000 chars
    test_embedding = get_ollama_embedding(test_content)
    vector_size = len(test_embedding)

    # Create collection
    create_collection_if_not_exists(client, collection_name, vector_size)

    # Process files
    points = []
    for i, file_path in enumerate(files_to_process):
        try:
            content = get_file_content(file_path)
            if not content.strip():
                continue

            # Truncate very long files
            if len(content) > 10000:
                content = content[:10000] + "..."

            # Generate embedding
            embedding = get_ollama_embedding(content)

            # Create unique ID based on file path and content hash
            file_id = hashlib.sha256(f"{file_path}:{hashlib.md5(content.encode()).hexdigest()}".encode()).hexdigest()

            # Create point
            point = PointStruct(
                id=file_id,
                vector=embedding,
                payload={
                    "file_path": str(file_path),
                    "relative_path": str(file_path.relative_to(project_path)),
                    "file_name": file_path.name,
                    "file_size": len(content),
                    "project": project_path.name,
                    "content_preview": content[:500] + "..." if len(content) > 500 else content
                }
            )
            points.append(point)

            print(f"Processed ({i+1}/{len(files_to_process)}): {file_path.relative_to(project_path)}")

            # Upload in batches of 10
            if len(points) >= 10:
                client.upsert(collection_name=collection_name, points=points)
                points = []

        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            continue

    # Upload remaining points
    if points:
        client.upsert(collection_name=collection_name, points=points)

    print(f"Completed processing {project_path}")

def main():
    """Main function to embed both projects."""
    print("Starting embedding process...")

    # Initialize Qdrant client
    print("Connecting to Qdrant...")
    client = QdrantClient("localhost", port=6333)
    print("✓ Qdrant connection established")

    # Test Ollama connection
    print("Testing Ollama connection...")
    try:
        test_embedding = get_ollama_embedding("test")
        print(f"✓ Ollama connection successful. Vector size: {len(test_embedding)}")
    except Exception as e:
        print(f"✗ Error connecting to Ollama: {e}")
        return

    # Process MCP project
    print("\n" + "="*50)
    print("PROCESSING MCP PROJECT")
    print("="*50)
    mcp_path = "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP"
    process_project(mcp_path, "mcp_project_qwen3", client)

    # Process yendorcats project
    print("\n" + "="*50)
    print("PROCESSING YENDORCATS PROJECT")
    print("="*50)
    yendorcats_path = "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats"
    process_project(yendorcats_path, "yendorcats_project_qwen3", client)

    print("\n" + "="*50)
    print("✓ All projects processed successfully!")
    print("="*50)

if __name__ == "__main__":
    main()