---

# Remote VSCode Development with Proxmox VE for MCP Server Setup

## Overview

Yes, it's **reasonable and safe** to connect VSCode remotely to your Proxmox VE server for setting up a self-hosted MCP server. This is a common development pattern that offers excellent performance and security when configured properly.

## Architecture Recommendation

### Optimal Setup
- **Local**: Qwen3 Embed 4B on MacBook Pro M3 (leveraging Apple Silicon efficiency)
- **Remote**: Qdrant vector database on Proxmox VE bare metal server
- **Connection**: VSCode Remote SSH to Proxmox VE container/VM

## Implementation Strategy

### 1. Proxmox VE Container Setup

Create a dedicated LXC container for your MCP server:

```bash
# Create Ubuntu 22.04 LXC container
pct create 100 local:vztmpl/ubuntu-22.04-standard_22.04-1_amd64.tar.zst \
  --hostname mcp-server \
  --memory 4096 \
  --cores 4 \
  --rootfs local-lvm:32 \
  --net0 name=eth0,bridge=vmbr0,ip=dhcp \
  --unprivileged 1
```

### 2. VSCode Remote SSH Configuration

Add to your `~/.ssh/config`:

```ssh
-config
Host proxmox-mcp
    HostName YOUR_PROXMOX_IP
    User root
    Port 22
    IdentityFile ~/.ssh/id_rsa
    ServerAliveInterval 60
    ServerAliveCountMax 3
```

### 3. Security Hardening

#### SSH Key Authentication
```bash
# Generate dedicated SSH key
ssh-keygen -t ed25519 -f ~/.ssh/proxmox_mcp -C "mcp-development"

# Copy to Proxmox container
ssh-copy-id -i ~/.ssh/proxmox_mcp.pub root@YOUR_PROXMOX_IP
```

#### Firewall Rules
```bash
# On Proxmox host - restrict SSH access
iptables -A INPUT -p tcp --dport 22 -s YOUR_MACBOOK_IP -j ACCEPT
iptables -A INPUT -p tcp --dport 22 -j DROP
```

## MCP Server Implementation

### 4. Qdrant Vector Database Setup

```
`yaml path=docker-compose.yml mode=EDIT
version: '3.8'
services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - ./qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    restart: unless-stopped
````

### 5. MCP Server Configuration

```
`json path=mcp-config.json mode=EDIT
{
  "servers": {
    "qdrant": {
      "command": "uvx",
      "args": ["mcp-server-qdrant"],
      "env": {
        "QDRANT_URL": "http://localhost:6333",
        "COLLECTION_NAME": "agent-memories",
        "EMBEDDING_MODEL": "sentence-transformers/all-MiniLM-L6-v2"
      }
    }
  }
}
````

### 6. Local Embedding Service

```python
`python path=embedding_service.py mode=EDIT
import requests
import json
from typing import List

class QwenEmbeddingService:
    def __init__(self, model_path: str, qdrant_url: str):
        self.model_path = model_path
        self.qdrant_url = qdrant_url
    
    def embed_and_store(self, texts: List[str], collection: str):
        # Local embedding with Qwen3
        embeddings = self.generate_embeddings(texts)
        
        # Store in remote Qdrant
        self.store_embeddings(embeddings, texts, collection)
    
    def generate_embeddings(self, texts: List[str]):
        # Implement Qwen3 embedding logic
        pass
    
    def store_embeddings(self, embeddings, texts, collection):
        # Store in remote Qdrant instance
        pass
````

## Security Best Practices

### Network Security
- **VPN**: Consider setting up WireGuard for encrypted tunnel
- **Firewall**: Restrict access to specific ports and IPs
- **SSH Hardening**: Disable password auth, use key-based only

### Container Isolation
- **Unprivileged LXC**: Use unprivileged containers for better security
- **Resource Limits**: Set CPU/memory limits to prevent resource exhaustion
- **Network Segmentation**: Isolate MCP services in dedicated VLAN

## Performance Optimization

### Local (MacBook M3)
- **Memory**: Ensure sufficient RAM for Qwen3 4B model
- **Thermal**: Monitor temperatures during embedding generation
- **Batch Processing**: Process embeddings in optimal batch sizes

### Remote (Proxmox)
- **Storage**: Use SSD storage for Qdrant data
- **Memory**: Allocate adequate RAM for vector operations
- **Network**: Ensure low-latency connection between local and remote

## Monitoring and Maintenance

### Health Checks
```bash
# Qdrant health check
curl -f http://localhost:6333/health

# MCP server status
systemctl status mcp-server
```

### Backup Strategy
- **Qdrant Data**: Regular snapshots of vector database
- **Configuration**: Version control for MCP configs
- **Proxmox Backups**: Automated container backups

## Tags
#mcp #qdrant #embeddings #proxmox #vscode #remote-development #vector-database #qwen3 #security #self-hosted

---
---

# Remote VSCode Development with Proxmox VE for MCP Server Setup

## Overview

Yes, it's **reasonable and safe** to connect VSCode remotely to your Proxmox VE server for setting up a self-hosted MCP server. This is a common development pattern that offers excellent performance and security when configured properly.

## Architecture Recommendation

### Optimal Setup
- **Local**: Qwen3 Embed 4B on MacBook Pro M3 (leveraging Apple Silicon efficiency)
- **Remote**: Qdrant vector database on Proxmox VE bare metal server
- **Connection**: VSCode Remote SSH to Proxmox VE container/VM

## Implementation Strategy

### 1. Proxmox VE Container Setup

Create a dedicated LXC container for your MCP server:

```bash
# Create Ubuntu 22.04 LXC container
pct create 100 local:vztmpl/ubuntu-22.04-standard_22.04-1_amd64.tar.zst \
  --hostname mcp-server \
  --memory 4096 \
  --cores 4 \
  --rootfs local-lvm:32 \
  --net0 name=eth0,bridge=vmbr0,ip=dhcp \
  --unprivileged 1
```

### 2. VSCode Remote SSH Configuration

Add to your `~/.ssh/config`:

```ssh-config
Host proxmox-mcp
    HostName YOUR_PROXMOX_IP
    User root
    Port 22
    IdentityFile ~/.ssh/id_rsa
    ServerAliveInterval 60
    ServerAliveCountMax 3
```

### 3. Security Hardening

#### SSH Key Authentication
```bash
# Generate dedicated SSH key
ssh-keygen -t ed25519 -f ~/.ssh/proxmox_mcp -C "mcp-development"

# Copy to Proxmox container
ssh-copy-id -i ~/.ssh/proxmox_mcp.pub root@YOUR_PROXMOX_IP
```

#### Firewall Rules
```bash
# On Proxmox host - restrict SSH access
iptables -A INPUT -p tcp --dport 22 -s YOUR_MACBOOK_IP -j ACCEPT
iptables -A INPUT -p tcp --dport 22 -j DROP
```

## MCP Server Implementation

### 4. Qdrant Vector Database Setup

````yaml path=docker-compose.yml mode=EDIT
version: '3.8'
services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - ./qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    restart: unless-stopped
````

### 5. MCP Server Configuration

````json path=mcp-config.json mode=EDIT
{
  "servers": {
    "qdrant": {
      "command": "uvx",
      "args": ["mcp-server-qdrant"],
      "env": {
        "QDRANT_URL": "http://localhost:6333",
        "COLLECTION_NAME": "agent-memories",
        "EMBEDDING_MODEL": "sentence-transformers/all-MiniLM-L6-v2"
      }
    }
  }
}
````

### 6. Local Embedding Service

````python path=embedding_service.py mode=EDIT
import requests
import json
from typing import List

class QwenEmbeddingService:
    def __init__(self, model_path: str, qdrant_url: str):
        self.model_path = model_path
        self.qdrant_url = qdrant_url
    
    def embed_and_store(self, texts: List[str], collection: str):
        # Local embedding with Qwen3
        embeddings = self.generate_embeddings(texts)
        
        # Store in remote Qdrant
        self.store_embeddings(embeddings, texts, collection)
    
    def generate_embeddings(self, texts: List[str]):
        # Implement Qwen3 embedding logic
        pass
    
    def store_embeddings(self, embeddings, texts, collection):
        # Store in remote Qdrant instance
        pass
````

## Security Best Practices

### Network Security
- **VPN**: Consider setting up WireGuard for encrypted tunnel
- **Firewall**: Restrict access to specific ports and IPs
- **SSH Hardening**: Disable password auth, use key-based only

### Container Isolation
- **Unprivileged LXC**: Use unprivileged containers for better security
- **Resource Limits**: Set CPU/memory limits to prevent resource exhaustion
- **Network Segmentation**: Isolate MCP services in dedicated VLAN

## Performance Optimization

### Local (MacBook M3)
- **Memory**: Ensure sufficient RAM for Qwen3 4B model
- **Thermal**: Monitor temperatures during embedding generation
- **Batch Processing**: Process embeddings in optimal batch sizes

### Remote (Proxmox)
- **Storage**: Use SSD storage for Qdrant data
- **Memory**: Allocate adequate RAM for vector operations
- **Network**: Ensure low-latency connection between local and remote

## Monitoring and Maintenance

### Health Checks
```bash
# Qdrant health check
curl -f http://localhost:6333/health

# MCP server status
systemctl status mcp-server
```

### Backup Strategy
- **Qdrant Data**: Regular snapshots of vector database
- **Configuration**: Version control for MCP configs
- **Proxmox Backups**: Automated container backups

## Tags
#mcp #qdrant #embeddings #proxmox #vscode #remote-development #vector-database #qwen3 #security #self-hosted

---
