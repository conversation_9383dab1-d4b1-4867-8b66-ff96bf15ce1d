# YendorCats Staging Environment Configuration
# This is a test configuration - replace with your actual values

# Environment Settings
ASPNETCORE_ENVIRONMENT=Staging
NODE_ENV=staging

# AWS/Backblaze B2 Storage Configuration
AWS_REGION=us-west-004
AWS_S3_BUCKET_NAME=yendor-staging-test
AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
AWS_S3_PUBLIC_URL=https://f004.backblazeb2.com/file/yendor-staging-test/{key}
AWS_S3_ACCESS_KEY=test-access-key
AWS_S3_SECRET_KEY=test-secret-key

# Backblaze B2 Native API
B2_BUCKET_NAME=yendor-staging-test
B2_APPLICATION_KEY_ID=test-key-id
B2_APPLICATION_KEY=test-application-key
B2_BUCKET_ID=test-bucket-id

# Security Configuration
YENDOR_JWT_SECRET=staging-super-secure-jwt-secret-key-32-characters-minimum-length-test

# Default Admin User
YENDOR_DEFAULT_ADMIN_USERNAME=admin
YENDOR_DEFAULT_ADMIN_EMAIL=<EMAIL>
YENDOR_DEFAULT_ADMIN_PASSWORD=TestPassword123!

# Application Features
SEEDING_ENABLE_SAMPLE_DATA=false
SEEDING_FORCE_RESEED=false
SEEDING_ENABLE_B2_SYNC=false

# Hybrid Storage Configuration
HYBRID_STORAGE_DEFAULT_PROVIDER=S3
HYBRID_STORAGE_ENABLE_DUAL_STORAGE=false
