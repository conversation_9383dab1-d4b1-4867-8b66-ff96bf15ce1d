{
  "mcpServers": {
    "mcp-server-qdrant": {
      "command": "uvx",
      "args": [
        "mcp-server-qdrant"
      ],
      "env": {
        "QDRANT_URL": "http://localhost:6333",
        "COLLECTION_NAME": "shared_code_4b",
        "EMBEDDING_MODEL": "dengcao/Qwen3-Embedding-4B:Q4_K_M"
      }
  "brave-search": {
    "command": "mcp-server-brave-search",
    "args": [],
    "env": {
      "BRAVE_SEARCH_URL": "https://api.search.brave.com/res/v1/web/search"
    }
  },
  "context7": {
    "command": "context7-mcp",
    "args": [],
    "env": {}
  },
  "filesystem": {
    "command": "mcp-server-filesystem",
    "args": [
      "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects"
    ],
    "env": {}
  },
  "mcp-fetch": {
    "command": "mcp-fetch",
    "args": [],
    "env": {}
  },
  "web-fetch": {
    "command": "uvx",
    "args": [
      "mcp-server-fetch"
    ],
    "env": {}
  },
  "github": {
    "command": "mcp-server-github",
    "args": [],
    "env": {}
  },
  "aws-ec2": {
    "command": "mcp-server-aws",
    "args": [],
    "env": {
      "AWS_DEFAULT_REGION": "us-east-1",
      "AWS_SERVICES": "ec2,s3,lambda,iam,cloudformation,rds,ecs"
    }
  },
  "aws-cli": {
    "command": "uvx",
    "args": [
      "mcp-server-aws-cli"
    ],
    "env": {
      "AWS_PROFILE": "default",
      "AWS_DEFAULT_REGION": "us-east-1"
    }
  },
  "secrets-1password": {
    "command": "mcp-server-1password",
    "args": [],
    "env": {}
  },
  "vault-secrets": {
    "command": "mcp-server-vault",
    "args": [],
    "env": {
      "VAULT_ADDR": "http://localhost:8200",
      "VAULT_NAMESPACE": "",
      "VAULT_TOKEN": "hvs.CAESIPsua2ckBXXzO6YEx1kzMdLLKgSFWNynwREOEvzfxR7EGh4KHGh2cy50YzhHZzRQUDhuY2g0Y0hvRm5iVU5jQ28"
    }
  },
  "bitwarden": {
    "command": "mcp-server-bitwarden",
    "args": [],
    "env": {}
  },
  "ollama": {
    "command": "node",
    "args": [
      "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP/ollama-mcp/build/index.js"
    ],
    "env": {
      "OLLAMA_HOST": "${OLLAMA_HOST:-http://127.0.0.1:11434}"
    }
  }
}
