#!/bin/bash

# RAG MCP Server Setup Script
# This script helps you set up RAG (Retrieval-Augmented Generation) MCP servers
# for technical documentation retrieval

echo "🚀 Setting up RAG MCP Servers for Technical Documentation"
echo "========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📋 You'll need to get API keys for the following services:${NC}"
echo ""

echo -e "${YELLOW}1. Brave Search API Key (FREE tier available)${NC}"
echo "   - Go to: https://brave.com/search/api/"
echo "   - Sign up for a free account"
echo "   - Get your API key from the dashboard"
echo "   - Free tier: 2,000 queries/month"
echo ""

echo -e "${YELLOW}2. Upstash Vector Database (FREE tier available)${NC}"
echo "   - Go to: https://upstash.com/"
echo "   - Sign up for a free account"
echo "   - Create a new Vector Database"
echo "   - Get your REST URL and Token"
echo "   - Free tier: 10,000 queries/month"
echo ""

echo -e "${GREEN}📦 Installed MCP Servers (All Globally Installed):${NC}"
echo "✅ Brave Search MCP Server - Web search and documentation retrieval"
echo "✅ Context7 MCP Server - Specialized documentation and code context"
echo "✅ Qdrant MCP Server - Local vector storage and semantic search"
echo "✅ Filesystem MCP Server - Local file system access and search"
echo "✅ MCP Fetch Server - Web content fetching and processing"
echo "✅ Web Fetch Server - Alternative web content retrieval"
echo ""

echo -e "${BLUE}🔧 Configuration Files:${NC}"
echo "📄 Main config: MCP/augment_mcp_config.json"
echo "📄 Shell alias: ~/.zshrc (auggie-mcp command)"
echo ""

echo -e "${YELLOW}⚙️  Next Steps:${NC}"
echo "1. Get your API keys from the URLs above"
echo "2. Edit MCP/augment_mcp_config.json and replace:"
echo "   - YOUR_BRAVE_API_KEY_HERE"
echo "   - YOUR_UPSTASH_VECTOR_URL_HERE" 
echo "   - YOUR_UPSTASH_VECTOR_TOKEN_HERE"
echo "3. Test with: auggie-mcp 'search for Python documentation on list comprehensions'"
echo ""

echo -e "${GREEN}🎯 What These Servers Enable:${NC}"
echo "• Real-time web search for latest documentation"
echo "• Technical documentation retrieval and analysis"
echo "• Code examples and best practices lookup"
echo "• API reference and usage examples"
echo "• Stack Overflow and GitHub issue search"
echo "• Local semantic memory for your projects"
echo "• Local filesystem access and search"
echo "• Web content fetching and processing"
echo "• Multi-source documentation aggregation"
echo ""

echo -e "${BLUE}💡 Example Usage:${NC}"
echo "auggie-mcp 'Find the latest React hooks documentation'"
echo "auggie-mcp 'Search for Python asyncio best practices'"
echo "auggie-mcp 'Get Docker compose examples for PostgreSQL'"
echo "auggie-mcp 'Find TypeScript interface examples'"
echo ""

echo -e "${GREEN}✨ Setup complete! Get your API keys and start using RAG-powered documentation retrieval!${NC}"
