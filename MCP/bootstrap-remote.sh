#!/bin/bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}MCP Remote Server Bootstrap Script${NC}"
echo -e "${BLUE}===================================${NC}\n"

# Update system packages
echo -e "${BLUE}Updating system packages...${NC}"
sudo apt-get update
sudo apt-get install -y curl jq git python3 python3-venv python3-pip unzip

# Install Node.js 20.x
if ! command -v node >/dev/null; then
  echo -e "${BLUE}Installing Node.js 20.x...${NC}"
  curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
  sudo apt-get install -y nodejs
else
  echo -e "${GREEN}Node.js already installed: $(node -v)${NC}"
fi

# Install Python UV package manager
echo -e "${BLUE}Installing UV package manager...${NC}"
curl -LsSf https://astral.sh/uv/install.sh | sh
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc

# Create Python virtual environment for MCP servers
echo -e "${BLUE}Setting up Python virtual environment...${NC}"
python3 -m venv $HOME/.venvs/mcp
$HOME/.venvs/mcp/bin/python -m pip install --upgrade pip

# Create configuration directories
echo -e "${BLUE}Creating MCP configuration directories...${NC}"
mkdir -p $HOME/.config/mcp
mkdir -p $HOME/mcp-servers

# Set AWS region defaults per your rule
echo -e "${BLUE}Setting AWS region defaults to ap-southeast-2...${NC}"
if ! grep -q "AWS_REGION" $HOME/.bashrc 2>/dev/null; then
  cat >> $HOME/.bashrc << 'EOF'

# AWS Region defaults
export AWS_REGION=ap-southeast-2
export AWS_DEFAULT_REGION=ap-southeast-2
EOF
fi

# Create secrets placeholder file
echo -e "${BLUE}Creating secrets template...${NC}"
cat > $HOME/.config/mcp/secrets.env << 'EOF'
# MCP Server API Keys
# Fill in actual values; do NOT commit this file.

# Claude API key (from Anthropic console)
ANTHROPIC_API_KEY=

# Google Gemini API key (from Google AI Studio)
GOOGLE_API_KEY=

# Alibaba Tongyi Qianwen (Qwen) API key
DASHSCOPE_API_KEY=

# OpenAI API key (for GPT models)
OPENAI_API_KEY=

# Brave Search API key (for web search MCP)
BRAVE_API_KEY=

# GitHub token (for GitHub MCP server)
GITHUB_TOKEN=

# Optional: 1Password Service Account Token
OP_SERVICE_ACCOUNT_TOKEN=

# AWS credentials (if not using IAM role)
#AWS_ACCESS_KEY_ID=
#AWS_SECRET_ACCESS_KEY=
EOF

# Auto-source secrets on login
if ! grep -q ".config/mcp/secrets.env" $HOME/.bashrc 2>/dev/null; then
  cat >> $HOME/.bashrc << 'EOF'

# Load MCP secrets
[ -f "$HOME/.config/mcp/secrets.env" ] && set -a && source "$HOME/.config/mcp/secrets.env" && set +a
EOF
fi

# Install common MCP servers
echo -e "${BLUE}Installing common MCP servers...${NC}"

# Python-based servers (using UV)
$HOME/.local/bin/uvx install mcp-server-qdrant || true
$HOME/.local/bin/uvx install mcp-server-fetch || true
$HOME/.local/bin/uvx install mcp-server-github || true
$HOME/.local/bin/uvx install mcp-server-aws || true
$HOME/.local/bin/uvx install mcp-server-aws-cli || true
$HOME/.local/bin/uvx install mcp-server-filesystem || true

# Node.js-based servers
npm install -g @modelcontextprotocol/server-brave-search || true
npm install -g @modelcontextprotocol/server-1password || true

echo -e "${GREEN}✅ Bootstrap complete!${NC}\n"
echo -e "${BLUE}Next steps:${NC}"
echo "1. Edit $HOME/.config/mcp/secrets.env with your API keys"
echo "2. Copy your MCP config.json to $HOME/.config/mcp/"
echo "3. Source your bashrc: source ~/.bashrc"
echo "4. Test MCP servers with: uvx mcp-server-fetch --help"
echo ""
echo -e "${BLUE}For SSH-based remote execution from Warp:${NC}"
echo "Ensure your local machine can SSH to this server with key-based auth:"
echo "  ssh-copy-id $(whoami)@$(hostname -I | awk '{print $1}')"