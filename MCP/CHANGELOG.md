# Changelog

All notable changes to the MCP configuration and servers will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [2.1.0] - 2025-01-27

### Added
- **Environment Variable Integration**: Proper environment variable support for Claude Desktop
  - Created `setup-claude-env.sh` for automated environment setup
  - Added `launch-claude-with-env.sh` for launching <PERSON> with environment variables
  - Created macOS application launcher for Claude MCP
  - Added shell aliases for convenient MCP operations
- **Enhanced Configuration Management**:
  - Updated `claude-augment-gemini-qwen-mcp_config_secure.json` to properly inherit environment variables
  - Removed shell-style variable expansion that doesn't work with Claude Desktop
  - Simplified configuration to rely on parent process environment
- **Improved User Experience**:
  - Automated Claude Desktop configuration deployment
  - Environment variable validation and checking
  - Service availability checking (Qdra<PERSON>, Vault, etc.)
  - Multiple launch options (script, alias, macOS app)

### Changed
- **Configuration Format**: Simplified MCP configuration to use inherited environment variables
- **Setup Process**: Streamlined environment setup with automated scripts
- **Documentation**: Updated setup instructions for environment variable usage

### Fixed
- **Environment Variable Handling**: Fixed Claude Desktop environment variable inheritance
- **Configuration Deployment**: Automated MCP configuration installation to Claude Desktop

## [2.0.0] - 2025-01-27

### Added
- **GitHub Integration**: Added `mcp-server-github` for full GitHub API access
  - Repository management capabilities
  - Issue and PR workflow support
  - Requires GitHub Personal Access Token
- **AWS Services Integration**: Added comprehensive AWS support
  - `mcp-server-aws`: Direct AWS service integration (EC2, S3, Lambda, IAM, CloudFormation, RDS, ECS)
  - `mcp-server-aws-cli`: AWS CLI-based MCP server for broader functionality
  - Support for both direct credentials and AWS profiles
- **Secrets Management**: Added multiple secrets management options
  - `mcp-server-1password`: Enterprise-grade secrets management (recommended)
  - `mcp-server-vault`: Self-hosted HashiCorp Vault integration
  - `mcp-server-bitwarden`: Open-source Bitwarden integration
- **Security Enhancements**:
  - Created `augment_mcp_config_secure.json` with environment variable support
  - Added `.env.template` for secure credential management
  - Implemented security best practices documentation
- **Documentation System**:
  - Added comprehensive setup guide (`mcp-servers-setup-guide.md`)
  - Created automated installation script (`install-mcp-servers.sh`)
  - Established changelog and feature tracking system
- **Development Tools**:
  - Added MCP Inspector integration for testing
  - Created validation and testing procedures

### Changed
- **Configuration Structure**: Enhanced MCP configuration with additional servers
- **Security Model**: Moved from hardcoded credentials to environment variable-based configuration
- **Installation Process**: Streamlined with automated installation script

### Security
- Implemented environment variable-based credential management
- Added multiple secrets management backend options
- Created security best practices documentation
- Established credential rotation guidelines

## [1.0.0] - 2025-01-27 (Initial State)

### Added
- **Core MCP Servers**:
  - `mcp-server-qdrant`: Vector database for semantic memory storage
  - `brave-search`: Web search capabilities
  - `context7`: Context management with Upstash vector storage
  - `filesystem`: File system access for Projects directory
  - `mcp-fetch` and `web-fetch`: Web content fetching tools
- **Basic Configuration**: Initial `augment_mcp_config.json` setup
- **Vector Storage**: Qdrant integration for semantic memory
- **Web Integration**: Brave search and web fetching capabilities

### Configuration
- Qdrant URL: http://localhost:6333
- Collection Name: augment-memories
- Embedding Model: sentence-transformers/all-MiniLM-L6-v2
- Filesystem Path: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects

---

## Version History Summary

| Version | Date | Major Changes |
|---------|------|---------------|
| 2.0.0 | 2025-01-27 | Added GitHub, AWS, and secrets management integration |
| 1.0.0 | 2025-01-27 | Initial MCP configuration with core servers |

## Migration Guide

### From 1.0.0 to 2.0.0

1. **Backup existing configuration**:
   ```bash
   cp augment_mcp_config.json augment_mcp_config_v1_backup.json
   ```

2. **Install new MCP servers**:
   ```bash
   ./install-mcp-servers.sh
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.template .env
   # Edit .env with your credentials
   ```

4. **Choose configuration approach**:
   - For production: Use `augment_mcp_config_secure.json`
   - For development: Update `augment_mcp_config.json` with your credentials

5. **Update Claude Desktop configuration**:
   ```bash
   cp augment_mcp_config_secure.json ~/Library/Application\ Support/Claude/claude_desktop_config.json
   ```

6. **Restart Claude Desktop** to load new configuration

## Breaking Changes

### 2.0.0
- **Configuration Format**: Added new servers require additional environment variables
- **Security Model**: Recommended migration to environment variable-based credentials
- **Dependencies**: New MCP servers require additional installations

## Deprecations

None at this time.

## Known Issues

### 2.0.0
- Some MCP servers may not be available in all package repositories yet
- AWS MCP servers require proper IAM permissions setup
- Secrets management servers require external service setup

## Support

For issues and questions:
1. Check the setup guide: `mcp-servers-setup-guide.md`
2. Review environment variables in `.env.template`
3. Test individual servers with MCP Inspector
4. Verify service dependencies (Qdrant, AWS CLI, etc.)
