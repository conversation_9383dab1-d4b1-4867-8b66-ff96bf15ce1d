{"mcpServers": {"github.com/NightTrek/Ollama-mcp": {"command": "node", "args": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP/ollama-mcp/build/index.js"], "env": {"OLLAMA_HOST": "http://127.0.0.1:11434"}, "disabled": false, "autoApprove": ["*"]}, "mcp-server-qdrant": {"command": "uvx", "args": ["mcp-server-qdrant"], "env": {"QDRANT_URL": "http://localhost:6333", "COLLECTION_NAME": "mcp-memories", "EMBEDDING_MODEL": "sentence-transformers/all-MiniLM-L6-v2"}, "disabled": false, "autoApprove": ["*"]}}}