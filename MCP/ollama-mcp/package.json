{"name": "ollama-mcp", "version": "0.1.0", "description": "An ollama MCP server designed to allow Cline or other MCP supporting tools to access ollama", "private": true, "type": "module", "bin": {"ollama-mcp": "./build/index.js"}, "files": ["build"], "scripts": {"build": "tsc && node -e \"import('node:fs').then(fs => fs.chmodSync('build/index.js', '755'))\"", "prepare": "npm run build", "watch": "tsc --watch", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "0.6.0", "axios": "^1.7.9"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}}