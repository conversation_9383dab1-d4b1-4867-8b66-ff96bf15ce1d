#!/bin/bash

# API Key Configuration Helper for RAG MCP Servers

CONFIG_FILE="MCP/augment_mcp_config.json"
BACKUP_FILE="MCP/augment_mcp_config.json.backup"

echo "🔑 RAG MCP Server API Key Configuration"
echo "======================================"

# Create backup
cp "$CONFIG_FILE" "$BACKUP_FILE"
echo "✅ Backup created: $BACKUP_FILE"

echo ""
echo "Please enter your API keys (press Enter to skip any):"
echo ""

# Brave API Key
read -p "🔍 Brave Search API Key: " BRAVE_KEY
if [ ! -z "$BRAVE_KEY" ]; then
    sed -i '' "s/YOUR_BRAVE_API_KEY_HERE/$BRAVE_KEY/g" "$CONFIG_FILE"
    echo "✅ Brave Search API key configured"
fi

# Upstash Vector URL
read -p "🗄️  Upstash Vector REST URL: " UPSTASH_URL
if [ ! -z "$UPSTASH_URL" ]; then
    sed -i '' "s|YOUR_UPSTASH_VECTOR_URL_HERE|$UPSTASH_URL|g" "$CONFIG_FILE"
    echo "✅ Upstash Vector URL configured"
fi

# Upstash Vector Token
read -p "🔐 Upstash Vector REST Token: " UPSTASH_TOKEN
if [ ! -z "$UPSTASH_TOKEN" ]; then
    sed -i '' "s/YOUR_UPSTASH_VECTOR_TOKEN_HERE/$UPSTASH_TOKEN/g" "$CONFIG_FILE"
    echo "✅ Upstash Vector token configured"
fi

echo ""
echo "🎉 Configuration updated!"
echo ""
echo "Test your setup with:"
echo "auggie-mcp 'search for the latest Python documentation'"
echo ""
echo "If you need to reconfigure, restore from backup:"
echo "cp $BACKUP_FILE $CONFIG_FILE"
