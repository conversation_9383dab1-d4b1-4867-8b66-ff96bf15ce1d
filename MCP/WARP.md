# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Quick Commands

### Install all MCP servers
```bash
# Install both Python and Node.js MCP servers
./install-mcp-servers.sh

# Or install manually:
# Python servers (via uvx)
uvx install mcp-server-qdrant
uvx install mcp-server-brave-search
uvx install mcp-server-fetch
uvx install mcp-server-filesystem
uvx install mcp-server-github
uvx install mcp-server-aws
uvx install mcp-server-aws-cli
uvx install mcp-server-1password
uvx install mcp-server-vault
uvx install mcp-server-bitwarden

# Node.js servers
npm install -g context7-mcp @modelcontextprotocol/inspector

# Build TypeScript server (ollama-mcp)
cd ollama-mcp
npm install
npm run build
```

### Set up Claude Desktop environment
```bash
# Create .env from template if needed
test -f .env || cp .env.template .env

# Edit .env with your API keys and set default AWS region
# Add: AWS_REGION=ap-southeast-2
# Add: AWS_DEFAULT_REGION=ap-southeast-2

# Run the automated setup script
./setup-claude-env.sh

# Or manually launch Claude with environment:
source ~/.claude-mcp-env
open -a "Claude"

# Use the convenience alias (after terminal restart)
claude-mcp
```

### Manage Qdrant vector database
```bash
# Start Qdrant
docker run -d --name qdrant -p 6333:6333 -p 6334:6334 \
  -v qdrant_storage:/qdrant/storage qdrant/qdrant

# Check health
curl http://localhost:6333/healthz

# Stop Qdrant
docker stop qdrant

# Remove container
docker rm qdrant
```

### Test MCP server configurations
```bash
# Test all servers
./test_mcp_servers.sh

# Test individual server with MCP Inspector
mcp-inspector uvx mcp-server-github

# Test with specific environment
env BRAVE_API_KEY="$BRAVE_API_KEY" mcp-inspector mcp-server-brave-search

# List all configured servers
jq -r '.mcpServers | keys[]' ~/Library/Application\ Support/Claude/claude_desktop_config.json
```

### Run individual MCP servers
```bash
# Python servers (via uvx)
uvx mcp-server-qdrant
uvx mcp-server-fetch
uvx mcp-server-github

# Node.js servers
context7-mcp

# Ollama TypeScript server
node ollama-mcp/build/index.js

# Qdrant server (local)
cd mcp-server-qdrant
uv run mcp-server-qdrant
```

### Switch between development and secure configurations
```bash
# Use secure configuration (with environment variables)
cp claude-augment-gemini-qwen-mcp_config_secure.json \
   ~/Library/Application\ Support/Claude/claude_desktop_config.json

# Use development configuration (with hardcoded values)
cp claude-augment-gemini-qwen-mcp_config.json \
   ~/Library/Application\ Support/Claude/claude_desktop_config.json

# Restart Claude Desktop after switching
```

### Validate configuration
```bash
# Check JSON syntax
jq . claude-augment-gemini-qwen-mcp_config_secure.json

# Verify environment variables are set
env | grep -E "BRAVE_API_KEY|GITHUB_TOKEN|AWS_"

# Test environment setup
./test-env-setup.sh
```

## Architecture

This is a Model Context Protocol (MCP) configuration repository that enables Claude Desktop to connect to multiple MCP servers for enhanced capabilities:

- **Semantic Memory**: Qdrant vector database for semantic search and memory persistence
- **GitHub Integration**: Full repository, issue, and PR management via mcp-server-github  
- **AWS Services**: EC2, S3, Lambda, IAM, CloudFormation, RDS, ECS management
- **Secrets Management**: Support for 1Password, HashiCorp Vault, and Bitwarden
- **Web Search**: Brave Search API integration for web content
- **File System**: Local project file operations with sandboxed access
- **LLM Bridge**: Ollama integration for local model inference

The repository structure:
- **Python servers** (`mcp-server-*`): Managed via `uv` and `uvx` package managers
- **Node.js servers** (`context7-mcp`): Installed globally via npm
- **TypeScript servers** (`ollama-mcp`): Built locally and run via Node.js
- **Configuration**: JSON files for Claude Desktop and `.env` for credentials
- **Setup scripts**: Automated installation and environment configuration

Default AWS region is configured as `ap-southeast-2` throughout.

## Development Workflows

### Adding a new MCP server
1. Create server directory with appropriate package files:
   - Python: `pyproject.toml` with MCP dependencies
   - Node.js: `package.json` with `@modelcontextprotocol/sdk`

2. Update installation script:
   ```bash
   # Edit install-mcp-servers.sh
   # Add to appropriate array (servers[] or node_servers[])
   ```

3. Add to Claude configuration:
   ```json
   "your-server": {
     "command": "uvx",
     "args": ["your-server-package"],
     "env": {
       "YOUR_API_KEY": "${YOUR_API_KEY}"
     }
   }
   ```

4. Add required environment variables to `.env`:
   ```bash
   echo "YOUR_API_KEY=your_value" >> .env
   ```

5. Test with MCP Inspector:
   ```bash
   mcp-inspector uvx your-server-package
   ```

### Testing changes
```bash
# After modifying servers or configuration
./test_mcp_servers.sh

# Test specific server in isolation
cd mcp-server-qdrant
uv run pytest tests/

# Test with Claude Desktop
./setup-claude-env.sh  # Reload configuration
claude-mcp              # Launch Claude with MCP
```

### Updating documentation
When making changes, update:
- `README.md` for user-facing changes
- `CHANGELOG.md` for version history
- `FEATURES.md` for new capabilities
- This `WARP.md` for command or workflow changes

## Dependencies and Tools

Required tools:
- **uv/uvx**: Python package management and script execution
- **Node.js 20+**: For Node.js/TypeScript MCP servers
- **npm**: Node package manager
- **Docker**: For running Qdrant vector database
- **jq**: JSON processing for configuration management
- **curl**: API testing and health checks

Key environment variables (configure in `.env`):
- `BRAVE_API_KEY`: Required for web search
- `GITHUB_TOKEN`: Required for GitHub integration
- `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`: For AWS services
- `AWS_REGION=ap-southeast-2`: Default AWS region
- `QDRANT_URL=http://localhost:6333`: Vector database endpoint
- `OLLAMA_HOST`: Ollama server endpoint (if using)

Configuration files:
- `.env`: Environment variables (create from `.env.template`)
- `claude-augment-gemini-qwen-mcp_config_secure.json`: Production config with env vars
- `claude-augment-gemini-qwen-mcp_config.json`: Development config with placeholders
- `~/Library/Application Support/Claude/claude_desktop_config.json`: Active Claude config