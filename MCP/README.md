# MCP Configuration for PaceySpace

A comprehensive Model Context Protocol (MCP) configuration providing semantic memory, GitHub integration, AWS services, and secrets management for PaceySpace Projects and Labs workflows.

## 🚀 Quick Start

1. **Install MCP servers**:
   ```bash
   ./install-mcp-servers.sh
   ```

2. **Setup environment and configuration**:
   ```bash
   ./setup-claude-env.sh
   ```
   This script will:
   - Create `.env` file from template (if needed)
   - Set up environment variable loading
   - Install MCP configuration to Claude Desktop
   - Create convenient launcher scripts and aliases

3. **Configure your API keys**:
   ```bash
   nano .env  # or code .env
   ```
   Add your actual API keys for:
   - `BRAVE_API_KEY` (required)
   - `GITHUB_TOKEN` (required)
   - AWS credentials (optional)
   - Secrets management tokens (optional)

4. **Launch Claude Desktop with MCP**:
   ```bash
   # Option 1: Use the launcher script
   ~/.claude-mcp-launcher

   # Option 2: Use alias (after restarting terminal)
   claude-mcp

   # Option 3: Use macOS app (from Applications folder)
   # "Claude MCP" application
   ```

## 🧠 Core Capabilities

### Semantic Memory & Vector Storage
- **Qdrant**: Local/cloud vector database for semantic memory
- **Context7**: Advanced context management with Upstash
- **Embedding Models**: Configurable semantic understanding

### Development & Version Control  
- **GitHub**: Complete repository, issue, and PR management
- **File System**: Local project file operations
- **Web Fetching**: Content retrieval and processing

### Cloud Infrastructure
- **AWS Services**: EC2, S3, Lambda, IAM, CloudFormation, RDS, ECS
- **AWS CLI**: Extended functionality through CLI interface
- **Multi-region**: Configurable AWS regions and profiles

### Secrets & Security
- **1Password**: Enterprise secrets management (recommended)
- **HashiCorp Vault**: Self-hosted secrets management
- **Bitwarden**: Open-source alternative
- **Environment Variables**: Secure credential handling

### Search & Information
- **Brave Search**: Privacy-focused web search
- **Vector Search**: Semantic information retrieval
- **Content Analysis**: Web content processing

## 📁 Project Structure

```
MCP/
├── 📋 Documentation
│   ├── README.md                           # This file - project overview
│   ├── CHANGELOG.md                        # Version history and changes
│   ├── FEATURES.md                         # Detailed feature documentation
│   ├── DOCUMENTATION.md                    # Documentation maintenance guide
│   └── mcp-servers-setup-guide.md          # Comprehensive setup instructions
├── ⚙️ Configuration
│   ├── augment_mcp_config.json             # Basic config (development)
│   ├── augment_mcp_config_secure.json      # Secure config (production)
│   └── .env.template                       # Environment variables template
├── 🔧 Tools
│   └── install-mcp-servers.sh              # Automated installation script
├── 🐍 Python Servers
│   ├── mcp-server-qdrant/                  # Vector database server
│   └── cost-explorer-mcp-server/           # AWS cost analysis
├── 🟨 Node.js Servers
│   ├── ollama-mcp/                         # Ollama integration
│   └── ollama-bridge/                      # HTTP bridge for Ollama
└── 🧪 Testing & Development
    ├── test_mcp_config.json                # Test configuration
    ├── test_mcp_servers.sh                 # Server testing script
    └── cline_mcp_settings.json             # Cline editor configuration
```

## 🛠️ MCP Servers

| Server | Language | Purpose | Status |
|--------|----------|---------|--------|
| mcp-server-qdrant | Python | Vector database & semantic memory | ✅ Active |
| mcp-server-github | Python | GitHub API integration | ✅ Active |
| mcp-server-aws | Python | AWS services management | ✅ Active |
| mcp-server-1password | Python | Secrets management | ✅ Active |
| brave-search | Python | Web search capabilities | ✅ Active |
| context7-mcp | Node.js | Advanced context management | ✅ Active |
| mcp-server-filesystem | Python | File system operations | ✅ Active |
| mcp-server-fetch | Python | Web content fetching | ✅ Active |
| ollama-mcp | TypeScript | Local LLM integration | ✅ Active |

## 🔐 Security Features

### Credential Management
- **Environment Variables**: Secure credential storage with automated setup
- **Multiple Secrets Backends**: 1Password, Vault, Bitwarden
- **Principle of Least Privilege**: Minimal required permissions
- **Credential Rotation**: Automated rotation support
- **Secure Launch**: Environment variables loaded only when needed

### Environment Variable Setup
- **Automated Configuration**: `setup-claude-env.sh` handles all setup
- **Validation**: Checks for required and optional environment variables
- **Multiple Launch Options**: Script, alias, or macOS application
- **Shell Integration**: Convenient aliases for daily use

### Best Practices
- No hardcoded secrets in configuration files
- Environment variables loaded from secure `.env` file
- Separate credentials for different environments
- Regular security audits and updates
- Comprehensive audit logging

## 📚 Documentation

### For Users
- **[Quick Start](#-quick-start)**: Get up and running quickly
- **[Features Guide](FEATURES.md)**: Detailed feature documentation
- **[Setup Guide](mcp-servers-setup-guide.md)**: Comprehensive installation instructions

### For Developers
- **[Changelog](CHANGELOG.md)**: Version history and breaking changes
- **[Documentation Guide](DOCUMENTATION.md)**: Maintenance and contribution guidelines
- **[Environment Template](.env.template)**: Configuration reference

### For Administrators
- **[Security Configuration](mcp-servers-setup-guide.md#security-best-practices)**: Security setup and best practices
- **[AWS Configuration](mcp-servers-setup-guide.md#aws-configuration)**: AWS integration setup
- **[Secrets Management](mcp-servers-setup-guide.md#secrets-management)**: Secrets backend configuration

## 🧪 Testing & Development

### Test Your Configuration
```bash
# Validate JSON syntax
cat augment_mcp_config_secure.json | jq .

# Test individual servers
mcp-inspector uvx mcp-server-github

# Run server tests
./test_mcp_servers.sh
```

### Development Tools
- **MCP Inspector**: Interactive testing interface
- **Server Testing Scripts**: Automated validation
- **Configuration Validation**: JSON schema checking

## 🔄 Version History

| Version | Date | Major Changes |
|---------|------|---------------|
| 2.0.0 | 2025-01-27 | Added GitHub, AWS, and secrets management |
| 1.0.0 | 2025-01-27 | Initial configuration with core servers |

See [CHANGELOG.md](CHANGELOG.md) for detailed version history.

## 🚨 Troubleshooting

### Common Issues
- **Connection Errors**: Verify services are running (Qdrant, Vault, etc.)
- **Authentication Errors**: Check API keys and tokens
- **Permission Errors**: Verify IAM roles and GitHub token scopes
- **Installation Errors**: Ensure uvx and npm are properly installed

### Getting Help
1. Check the [Setup Guide](mcp-servers-setup-guide.md)
2. Review [Environment Variables](.env.template)
3. Test with [MCP Inspector](https://github.com/modelcontextprotocol/inspector)
4. Validate service dependencies

## 🤝 Contributing

### Documentation Updates
1. Follow the [Documentation Guide](DOCUMENTATION.md)
2. Update relevant documentation files
3. Add entries to [CHANGELOG.md](CHANGELOG.md)
4. Test installation and setup procedures

### Adding New Servers
1. Update configuration files
2. Add installation steps to `install-mcp-servers.sh`
3. Document in [FEATURES.md](FEATURES.md)
4. Update this README

## 📞 Support

- **Issues**: Report bugs and request features via GitHub issues
- **Documentation**: Questions about setup and configuration
- **Security**: Report security issues privately
- **General**: Use GitHub discussions for general questions

## 📄 License

This configuration is part of the PaceySpace brain-preservatives ecosystem. See individual MCP server repositories for their specific licenses.

---

**PaceySpace Projects & Labs** | Self-hosted on-prem infrastructure | Brain-preservatives ecosystem
