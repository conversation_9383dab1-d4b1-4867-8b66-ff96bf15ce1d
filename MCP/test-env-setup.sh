#!/bin/bash

# Test Environment Variable Setup
# This script validates that the MCP environment variable setup is working correctly

set -e

echo "🧪 Testing MCP Environment Variable Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

test_count=0
pass_count=0
fail_count=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    test_count=$((test_count + 1))
    print_status "Running: $test_name"
    
    if eval "$test_command"; then
        print_success "$test_name"
        pass_count=$((pass_count + 1))
    else
        print_error "$test_name"
        fail_count=$((fail_count + 1))
    fi
    echo
}

# Test 1: Check if .env.template exists
run_test "Environment template exists" "[ -f '.env.template' ]"

# Test 2: Check if setup script exists and is executable
run_test "Setup script exists and is executable" "[ -x 'setup-claude-env.sh' ]"

# Test 3: Check if .env file exists (after setup)
if [ -f ".env" ]; then
    run_test ".env file exists" "true"
    
    # Test 4: Check if .env file has required variables
    run_test ".env contains BRAVE_API_KEY" "grep -q 'BRAVE_API_KEY=' .env"
    run_test ".env contains GITHUB_TOKEN" "grep -q 'GITHUB_TOKEN=' .env"
    
    # Test 5: Load environment and check variables
    print_status "Loading environment variables from .env..."
    source .env
    
    # Check if variables are loaded (even if empty)
    run_test "BRAVE_API_KEY variable loaded" "[ -n \"\${BRAVE_API_KEY+x}\" ]"
    run_test "GITHUB_TOKEN variable loaded" "[ -n \"\${GITHUB_TOKEN+x}\" ]"
    
    # Check if variables have actual values (not just placeholders)
    if [[ "$BRAVE_API_KEY" != *"your_brave_api_key_here"* ]] && [ -n "$BRAVE_API_KEY" ]; then
        print_success "BRAVE_API_KEY appears to be configured"
    else
        print_warning "BRAVE_API_KEY not configured (still has placeholder value)"
    fi
    
    if [[ "$GITHUB_TOKEN" != *"your_github_token_here"* ]] && [ -n "$GITHUB_TOKEN" ]; then
        print_success "GITHUB_TOKEN appears to be configured"
    else
        print_warning "GITHUB_TOKEN not configured (still has placeholder value)"
    fi
    
else
    print_warning ".env file not found - run setup-claude-env.sh first"
fi

# Test 6: Check if Claude Desktop environment script exists
claude_env_script="$HOME/.claude-mcp-env"
run_test "Claude environment script exists" "[ -f '$claude_env_script' ]"

# Test 7: Check if Claude Desktop launcher exists
claude_launcher="$HOME/.claude-mcp-launcher"
run_test "Claude launcher script exists" "[ -f '$claude_launcher' ]"

# Test 8: Check if Claude Desktop configuration exists
claude_config="$HOME/Library/Application Support/Claude/claude_desktop_config.json"
run_test "Claude Desktop configuration exists" "[ -f '$claude_config' ]"

# Test 9: Validate Claude Desktop configuration JSON
if [ -f "$claude_config" ]; then
    run_test "Claude Desktop configuration is valid JSON" "jq empty '$claude_config'"
    
    # Check if configuration has expected servers
    run_test "Configuration contains mcp-server-qdrant" "jq -e '.mcpServers[\"mcp-server-qdrant\"]' '$claude_config' > /dev/null"
    run_test "Configuration contains brave-search" "jq -e '.mcpServers[\"brave-search\"]' '$claude_config' > /dev/null"
    run_test "Configuration contains github" "jq -e '.mcpServers[\"github\"]' '$claude_config' > /dev/null"
fi

# Test 10: Check if required tools are installed
run_test "uvx is installed" "command -v uvx > /dev/null"
run_test "jq is installed" "command -v jq > /dev/null"

# Test 11: Check if MCP servers are installed
print_status "Checking MCP server installations..."

mcp_servers=(
    "mcp-server-qdrant"
    "mcp-server-github"
    "mcp-server-brave-search"
)

for server in "${mcp_servers[@]}"; do
    if uvx list 2>/dev/null | grep -q "$server"; then
        print_success "$server is installed"
    else
        print_warning "$server not found in uvx list"
    fi
done

# Test 12: Check service availability
print_status "Checking service availability..."

# Check Qdrant
if curl -s "http://localhost:6333/healthz" > /dev/null 2>&1; then
    print_success "Qdrant is running on localhost:6333"
else
    print_warning "Qdrant not running (start with: docker run -p 6333:6333 qdrant/qdrant)"
fi

# Check if macOS app was created
if [[ "$OSTYPE" == "darwin"* ]]; then
    app_dir="$HOME/Applications/Claude MCP.app"
    run_test "macOS Claude MCP app exists" "[ -d '$app_dir' ]"
fi

# Summary
echo "🏁 Test Summary"
echo "==============="
echo "Total tests: $test_count"
echo "Passed: $pass_count"
echo "Failed: $fail_count"

if [ $fail_count -eq 0 ]; then
    print_success "All tests passed! ✨"
    echo ""
    echo "🚀 Your MCP environment setup is working correctly!"
    echo ""
    echo "Next steps:"
    echo "1. Configure your API keys in .env file"
    echo "2. Start required services (Qdrant, etc.)"
    echo "3. Launch Claude Desktop with: claude-mcp"
    echo ""
else
    print_error "Some tests failed. Please check the setup."
    echo ""
    echo "Common fixes:"
    echo "1. Run: ./setup-claude-env.sh"
    echo "2. Install missing tools: brew install jq"
    echo "3. Install MCP servers: ./install-mcp-servers.sh"
    echo ""
    exit 1
fi
