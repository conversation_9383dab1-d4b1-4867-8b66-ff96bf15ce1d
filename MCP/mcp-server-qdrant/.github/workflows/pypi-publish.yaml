# This workflow will upload a Python Package using Twine when a release is created
# For more information see: https://help.github.com/en/actions/language-and-framework-guides/using-python-with-github-actions#publishing-to-package-registries

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: PyPI Publish

on:
  workflow_dispatch:
  push:
    # Pattern matched against refs/tags
    tags:
      - 'v*'           # Push events to every version tag

env:
  UV_PUBLISH_TOKEN: '${{ secrets.PYPI_API_TOKEN }}'

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.10.x'

    - name: Install dependencies
      run: |
        python -m pip install uv
        uv sync

    - name: Build package
      run: uv build

    - name: Publish package
      run: uv publish
