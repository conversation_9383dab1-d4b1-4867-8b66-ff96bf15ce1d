#!/bin/bash

# Documentation Maintenance Script
# Helps maintain consistency and quality across all MCP documentation

set -e

echo "📚 MCP Documentation Maintenance"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_tools() {
    print_status "Checking required tools..."
    
    local missing_tools=()
    
    if ! command -v jq &> /dev/null; then
        missing_tools+=("jq")
    fi
    
    if ! command -v markdownlint &> /dev/null; then
        print_warning "markdownlint not found. Install with: npm install -g markdownlint-cli"
    fi
    
    if ! command -v markdown-link-check &> /dev/null; then
        print_warning "markdown-link-check not found. Install with: npm install -g markdown-link-check"
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        exit 1
    fi
    
    print_success "Tool check completed"
}

# Validate JSON configuration files
validate_json() {
    print_status "Validating JSON configuration files..."
    
    local json_files=(
        "augment_mcp_config.json"
        "augment_mcp_config_secure.json"
        "test_mcp_config.json"
        "cline_mcp_settings.json"
    )
    
    for file in "${json_files[@]}"; do
        if [ -f "$file" ]; then
            if jq empty "$file" 2>/dev/null; then
                print_success "✓ $file is valid JSON"
            else
                print_error "✗ $file has invalid JSON syntax"
                return 1
            fi
        else
            print_warning "⚠ $file not found"
        fi
    done
}

# Check markdown files for common issues
check_markdown() {
    print_status "Checking markdown files..."
    
    local md_files=(
        "README.md"
        "CHANGELOG.md"
        "FEATURES.md"
        "DOCUMENTATION.md"
        "mcp-servers-setup-guide.md"
    )
    
    for file in "${md_files[@]}"; do
        if [ -f "$file" ]; then
            print_status "Checking $file..."
            
            # Check for basic structure
            if ! grep -q "^# " "$file"; then
                print_warning "⚠ $file missing main heading"
            fi
            
            # Check for modification date (if it has frontmatter)
            if grep -q "^---" "$file"; then
                if ! grep -q "modification_date:" "$file"; then
                    print_warning "⚠ $file missing modification_date in frontmatter"
                fi
            fi
            
            print_success "✓ $file checked"
        else
            print_error "✗ $file not found"
        fi
    done
}

# Check for broken internal links
check_internal_links() {
    print_status "Checking internal links..."
    
    local md_files=(
        "README.md"
        "CHANGELOG.md"
        "FEATURES.md"
        "DOCUMENTATION.md"
        "mcp-servers-setup-guide.md"
    )
    
    for file in "${md_files[@]}"; do
        if [ -f "$file" ]; then
            # Extract markdown links and check if referenced files exist
            grep -oE '\[.*\]\([^)]+\)' "$file" | grep -oE '\([^)]+\)' | tr -d '()' | while read -r link; do
                # Skip external links (http/https)
                if [[ $link =~ ^https?:// ]]; then
                    continue
                fi
                
                # Skip anchors
                if [[ $link =~ ^# ]]; then
                    continue
                fi
                
                # Check if file exists
                if [ ! -f "$link" ]; then
                    print_warning "⚠ Broken link in $file: $link"
                fi
            done
        fi
    done
}

# Update modification dates
update_modification_dates() {
    print_status "Updating modification dates..."
    
    local current_date=$(date +%Y-%m-%d)
    
    local files_with_frontmatter=(
        "DOCUMENTATION.md"
    )
    
    for file in "${files_with_frontmatter[@]}"; do
        if [ -f "$file" ]; then
            if grep -q "modification_date:" "$file"; then
                # Update existing modification date
                sed -i.bak "s/modification_date: .*/modification_date: $current_date/" "$file"
                rm -f "$file.bak"
                print_success "✓ Updated modification date in $file"
            fi
        fi
    done
}

# Generate documentation metrics
generate_metrics() {
    print_status "Generating documentation metrics..."
    
    echo ""
    echo "📊 Documentation Metrics"
    echo "========================"
    
    # Count total lines of documentation
    local total_lines=0
    local md_files=(
        "README.md"
        "CHANGELOG.md"
        "FEATURES.md"
        "DOCUMENTATION.md"
        "mcp-servers-setup-guide.md"
    )
    
    for file in "${md_files[@]}"; do
        if [ -f "$file" ]; then
            local lines=$(wc -l < "$file")
            echo "📄 $file: $lines lines"
            total_lines=$((total_lines + lines))
        fi
    done
    
    echo "📊 Total documentation: $total_lines lines"
    
    # Count configuration files
    local config_files=(
        "augment_mcp_config.json"
        "augment_mcp_config_secure.json"
        ".env.template"
    )
    
    echo "⚙️ Configuration files: ${#config_files[@]}"
    
    # Count MCP servers configured
    if [ -f "augment_mcp_config_secure.json" ]; then
        local server_count=$(jq '.mcpServers | length' augment_mcp_config_secure.json)
        echo "🔧 MCP servers configured: $server_count"
    fi
    
    echo ""
}

# Main execution
main() {
    echo "Starting documentation maintenance..."
    echo ""
    
    check_tools
    echo ""
    
    validate_json
    echo ""
    
    check_markdown
    echo ""
    
    check_internal_links
    echo ""
    
    if [ "$1" = "--update-dates" ]; then
        update_modification_dates
        echo ""
    fi
    
    generate_metrics
    
    print_success "Documentation maintenance completed!"
    echo ""
    echo "💡 Tips:"
    echo "   - Run with --update-dates to update modification dates"
    echo "   - Install markdownlint for better markdown checking"
    echo "   - Install markdown-link-check for external link validation"
    echo "   - Use 'jq .' to pretty-print JSON configuration files"
}

# Run main function with all arguments
main "$@"
