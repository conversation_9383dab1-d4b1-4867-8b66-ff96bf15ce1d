#!/bin/bash

# Launch Claude Desktop with Environment Variables
# This script loads environment variables and launches Claude Desktop with MCP configuration

set -e

echo "🚀 Launching Claude Desktop with MCP Environment Variables"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    echo "Please create .env file from template:"
    echo "  cp .env.template .env"
    echo "  # Edit .env with your API keys"
    exit 1
fi

print_status "Loading environment variables from .env file..."

# Load environment variables from .env file
set -a  # automatically export all variables
source .env
set +a  # stop automatically exporting

print_success "Environment variables loaded"

# Verify critical environment variables are set
print_status "Checking required environment variables..."

required_vars=(
    "GITHUB_TOKEN"
)

optional_vars=(
    "BRAVE_API_KEY"
    "UPSTASH_VECTOR_REST_URL"
    "UPSTASH_VECTOR_REST_TOKEN"
    "AWS_ACCESS_KEY_ID"
    "AWS_SECRET_ACCESS_KEY"
    "OP_SERVICE_ACCOUNT_TOKEN"
    "VAULT_TOKEN"
    "BW_CLIENTID"
)

missing_required=()
missing_optional=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_required+=("$var")
    else
        print_success "✓ $var is set"
    fi
done

for var in "${optional_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_optional+=("$var")
    else
        print_success "✓ $var is set"
    fi
done

if [ ${#missing_required[@]} -ne 0 ]; then
    print_error "Missing required environment variables:"
    for var in "${missing_required[@]}"; do
        echo "  - $var"
    done
    echo ""
    echo "Please set these variables in your .env file"
    exit 1
fi

if [ ${#missing_optional[@]} -ne 0 ]; then
    print_warning "Missing optional environment variables:"
    for var in "${missing_optional[@]}"; do
        echo "  - $var"
    done
    echo ""
    echo "These services will not be available until configured"
fi

# Fetch secrets from Vault if VAULT_TOKEN is set
print_status "Checking for secrets from Vault..."
if [ -n "$VAULT_TOKEN" ]; then
    print_status "VAULT_TOKEN is set. Fetching secrets..."
    # Ensure VAULT_ADDR is set, default if not
    VAULT_ADDR=${VAULT_ADDR:-http://127.0.0.1:8200}
    export BRAVE_API_KEY=$(VAULT_ADDR="$VAULT_ADDR" VAULT_TOKEN="$VAULT_TOKEN" vault kv get -field=api_key mcp/brave-search)
    if [ -n "$BRAVE_API_KEY" ]; then
        print_success "✓ Successfully fetched BRAVE_API_KEY from Vault"
    else
        print_warning "⚠ Failed to fetch BRAVE_API_KEY from Vault. The 'brave-search' service may not work."
    fi
else
    print_warning "VAULT_TOKEN is not set. Skipping secret fetch from Vault. Using .env values."
fi

# Check if Claude Desktop configuration exists
claude_config_dir="$HOME/Library/Application Support/Claude"
claude_config_file="$claude_config_dir/claude_desktop_config.json"

if [ ! -f "$claude_config_file" ]; then
    print_warning "Claude Desktop configuration not found"
    echo "Creating configuration directory and copying MCP config..."
    
    mkdir -p "$claude_config_dir"
    cp "claude-augment-gemini-qwen-mcp_config_secure.json" "$claude_config_file"
    
    print_success "MCP configuration copied to Claude Desktop"
else
    print_status "Claude Desktop configuration exists"
    
    # Ask if user wants to update the configuration
    read -p "Update Claude Desktop configuration with current MCP config? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cp "claude-augment-gemini-qwen-mcp_config_secure.json" "$claude_config_file"
        print_success "Claude Desktop configuration updated"
    fi
fi

# Check if required services are running
print_status "Checking service availability..."

# Check Qdrant
if curl -s "http://localhost:6333/healthz" > /dev/null 2>&1; then
    print_success "✓ Qdrant is running on localhost:6333"
else
    print_warning "⚠ Qdrant not running. Start with: docker run -p 6333:6333 qdrant/qdrant"
fi

# Check Vault (if configured)
if [ -n "$VAULT_TOKEN" ]; then
    if curl -s "$VAULT_ADDR/v1/sys/health" > /dev/null 2>&1; then
        print_success "✓ Vault is running at $VAULT_ADDR"
    else
        print_warning "⚠ Vault not accessible at $VAULT_ADDR"
    fi
fi

# Export environment variables for Claude Desktop
print_status "Exporting environment variables for Claude Desktop..."

# Create a temporary script to launch Claude Desktop with environment
temp_script=$(mktemp)
cat > "$temp_script" << EOF
#!/bin/bash

# Export all environment variables
$(env | grep -E '^(BRAVE_API_KEY|GITHUB_TOKEN|UPSTASH_|AWS_|OP_|VAULT_|BW_|QDRANT_)' | sed 's/^/export /')

# Launch Claude Desktop
open -a "Claude"
EOF

chmod +x "$temp_script"

print_status "Launching Claude Desktop with environment variables..."

# Execute the temporary script
"$temp_script"

# Clean up
rm "$temp_script"

print_success "Claude Desktop launched with MCP environment!"
echo ""
echo "📋 Next Steps:"
echo "1. Wait for Claude Desktop to start"
echo "2. Check that MCP servers are connected in Claude"
echo "3. Test functionality with MCP tools"
echo ""
echo "🔧 Troubleshooting:"
echo "- If servers don't connect, check the Claude Desktop logs"
echo "- Verify environment variables are set correctly"
echo "- Ensure required services (Qdrant, Vault) are running"
echo "- Check MCP server installation with: uvx list"
echo ""
echo "📚 Documentation:"
echo "- Setup Guide: mcp-servers-setup-guide.md"
echo "- Features: FEATURES.md"
echo "- Troubleshooting: README.md"
