import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3000;
const OLLAMA_HOST = 'http://127.0.0.1:11434';

// Middleware
app.use(cors());
app.use(express.json());

// Helper function to make requests to Ollama
async function ollamaRequest(endpoint, options = {}) {
  const response = await fetch(`${OLLAMA_HOST}${endpoint}`, {
    method: options.method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    body: options.body ? JSON.stringify(options.body) : undefined
  });
  
  if (!response.ok) {
    throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Check if Ollama is running
    await ollamaRequest('/api/tags');
    res.json({ 
      status: 'Bridge server is running', 
      ollama: 'connected',
      timestamp: new Date().toISOString() 
    });
  } catch (error) {
    res.json({ 
      status: 'Bridge server is running', 
      ollama: 'disconnected',
      error: error.message,
      timestamp: new Date().toISOString() 
    });
  }
});

// Text completion endpoint
app.post('/complete', async (req, res) => {
  try {
    const { text, model = 'llama3.1:8b', maxTokens = 100 } = req.body;
    
    if (!text) {
      return res.status(400).json({ error: 'Text prompt is required' });
    }

    // Call Ollama's generate API
    const response = await ollamaRequest('/api/generate', {
      method: 'POST',
      body: {
        model: model,
        prompt: text,
        stream: false,
        options: {
          num_predict: maxTokens
        }
      }
    });

    res.json({
      text: text,
      completion: response.response,
      model: model,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in completion:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});

// Ollama models list endpoint
app.get('/models', async (req, res) => {
  try {
    const response = await ollamaRequest('/api/tags');
    const models = response.models.map(model => model.name);
    res.json({ models });
  } catch (error) {
    console.error('Error listing models:', error);
    res.status(500).json({ error: error.message || 'Failed to list models' });
  }
});

app.listen(PORT, () => {
  console.log(`Ollama MCP Bridge running on http://localhost:${PORT}`);
  console.log('Endpoints:');
  console.log(`  GET  /health  - Health check`);
  console.log(`  POST /complete - Text completion`);
  console.log(`  GET  /models - Available models`);
});
