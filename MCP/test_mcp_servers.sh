#!/bin/bash

# Test script for all installed MCP servers

echo "🧪 Testing All Installed MCP Servers"
echo "===================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo ""
echo -e "${YELLOW}Testing server availability...${NC}"

# Test each server command
servers=(
    "mcp-server-brave-search:Brave Search"
    "context7-mcp:Context7"
    "mcp-server-filesystem:Filesystem"
    "mcp-fetch:MCP Fetch"
    "uvx mcp-server-qdrant:Qdrant (via uvx)"
    "uvx mcp-server-fetch:Web Fetch (via uvx)"
)

for server_info in "${servers[@]}"; do
    IFS=':' read -r command name <<< "$server_info"
    echo -n "Testing $name... "
    
    if command -v ${command%% *} >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Available${NC}"
    else
        echo -e "${RED}❌ Not found${NC}"
    fi
done

echo ""
echo -e "${YELLOW}Testing Qdrant connection...${NC}"
if curl -s http://localhost:6333/healthz >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Qdrant server is running${NC}"
else
    echo -e "${RED}❌ Qdrant server not accessible${NC}"
    echo "   Start with: docker run -p 6333:6333 qdrant/qdrant"
fi

echo ""
echo -e "${YELLOW}Testing MCP configuration...${NC}"
config_file="MCP/augment_mcp_config.json"
if [ -f "$config_file" ]; then
    echo -e "${GREEN}✅ Configuration file exists${NC}"
    
    # Check for placeholder API keys
    if grep -q "YOUR_.*_HERE" "$config_file"; then
        echo -e "${YELLOW}⚠️  API keys need to be configured${NC}"
        echo "   Run: ./MCP/configure_api_keys.sh"
    else
        echo -e "${GREEN}✅ API keys appear to be configured${NC}"
    fi
else
    echo -e "${RED}❌ Configuration file not found${NC}"
fi

echo ""
echo -e "${YELLOW}Testing shell alias...${NC}"
if alias auggie-mcp >/dev/null 2>&1; then
    echo -e "${GREEN}✅ auggie-mcp alias is available${NC}"
else
    echo -e "${RED}❌ auggie-mcp alias not found${NC}"
    echo "   Run: source ~/.zshrc"
fi

echo ""
echo -e "${GREEN}🎯 Ready to test with:${NC}"
echo "auggie-mcp 'test the filesystem server by listing files'"
echo "auggie-mcp 'fetch content from a documentation website'"
echo ""
