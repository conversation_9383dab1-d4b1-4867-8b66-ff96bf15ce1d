#!/bin/bash

# Setup Claude Desktop Environment Variables
# This script creates a proper environment setup for Claude Desktop MCP integration

set -e

echo "🔧 Setting up Claude Desktop Environment for MCP"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    echo ""
    echo "Creating .env from template..."
    if [ -f ".env.template" ]; then
        cp .env.template .env
        print_success ".env file created from template"
        echo ""
        print_warning "Please edit .env file with your actual API keys and tokens:"
        echo "  nano .env"
        echo "  # or"
        echo "  code .env"
        echo ""
        echo "Required variables to set:"
        echo "  - BRAVE_API_KEY"
        echo "  - GITHUB_TOKEN"
        echo "  - AWS credentials (if using AWS)"
        echo "  - Secrets management tokens (if using)"
        exit 1
    else
        print_error ".env.template not found!"
        exit 1
    fi
fi

print_status "Loading environment variables from .env file..."

# Load and validate environment variables
source .env

# Create environment export script for Claude Desktop
claude_env_script="$HOME/.claude-mcp-env"

print_status "Creating Claude Desktop environment script..."

cat > "$claude_env_script" << 'EOF'
#!/bin/bash
# Claude Desktop MCP Environment Variables
# This file is automatically generated by setup-claude-env.sh

EOF

# Add environment variables to the script
while IFS= read -r line; do
    # Skip comments and empty lines
    if [[ $line =~ ^[[:space:]]*# ]] || [[ -z "${line// }" ]]; then
        continue
    fi
    
    # Extract variable name and value
    if [[ $line =~ ^([^=]+)=(.*)$ ]]; then
        var_name="${BASH_REMATCH[1]}"
        var_value="${BASH_REMATCH[2]}"
        
        # Only export non-empty values
        if [ -n "$var_value" ]; then
            echo "export $var_name=\"$var_value\"" >> "$claude_env_script"
        fi
    fi
done < .env

chmod +x "$claude_env_script"

print_success "Environment script created at $claude_env_script"

# Create Claude Desktop launcher script
claude_launcher="$HOME/.claude-mcp-launcher"

print_status "Creating Claude Desktop launcher script..."

cat > "$claude_launcher" << EOF
#!/bin/bash
# Claude Desktop MCP Launcher
# This script loads environment variables and launches Claude Desktop

# Load MCP environment variables
source "$claude_env_script"

# Launch Claude Desktop
open -a "Claude"
EOF

chmod +x "$claude_launcher"

print_success "Launcher script created at $claude_launcher"

# Update Claude Desktop configuration
claude_config_dir="$HOME/Library/Application Support/Claude"
claude_config_file="$claude_config_dir/claude_desktop_config.json"

print_status "Setting up Claude Desktop configuration..."

mkdir -p "$claude_config_dir"

# Copy the secure configuration
cp "claude-augment-gemini-qwen-mcp_config_secure.json" "$claude_config_file"

print_success "MCP configuration installed to Claude Desktop"

# Create shell profile additions
shell_profile=""
if [ -f "$HOME/.zshrc" ]; then
    shell_profile="$HOME/.zshrc"
elif [ -f "$HOME/.bash_profile" ]; then
    shell_profile="$HOME/.bash_profile"
elif [ -f "$HOME/.bashrc" ]; then
    shell_profile="$HOME/.bashrc"
fi

if [ -n "$shell_profile" ]; then
    print_status "Adding MCP aliases to $shell_profile..."
    
    # Check if aliases already exist
    if ! grep -q "# MCP Claude Desktop aliases" "$shell_profile"; then
        cat >> "$shell_profile" << EOF

# MCP Claude Desktop aliases
alias claude-mcp="$claude_launcher"
alias claude-env="source $claude_env_script"
alias claude-config="code '$claude_config_file'"
EOF
        print_success "Aliases added to $shell_profile"
        echo ""
        echo "Available aliases:"
        echo "  claude-mcp    - Launch Claude Desktop with MCP environment"
        echo "  claude-env    - Load MCP environment variables in current shell"
        echo "  claude-config - Edit Claude Desktop MCP configuration"
    else
        print_warning "MCP aliases already exist in $shell_profile"
    fi
fi

# Create desktop application (macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    print_status "Creating macOS application launcher..."
    
    app_dir="$HOME/Applications/Claude MCP.app"
    mkdir -p "$app_dir/Contents/MacOS"
    
    # Create Info.plist
    cat > "$app_dir/Contents/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>claude-mcp</string>
    <key>CFBundleIdentifier</key>
    <string>com.paceyspace.claude-mcp</string>
    <key>CFBundleName</key>
    <string>Claude MCP</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
</dict>
</plist>
EOF

    # Create executable
    cat > "$app_dir/Contents/MacOS/claude-mcp" << EOF
#!/bin/bash
source "$claude_env_script"
open -a "Claude"
EOF

    chmod +x "$app_dir/Contents/MacOS/claude-mcp"
    
    print_success "macOS application created: $app_dir"
fi

# Validate environment setup
print_status "Validating environment setup..."

source "$claude_env_script"

required_vars=("BRAVE_API_KEY" "GITHUB_TOKEN")
optional_vars=("AWS_ACCESS_KEY_ID" "UPSTASH_VECTOR_REST_URL" "OP_SERVICE_ACCOUNT_TOKEN")

missing_required=()
missing_optional=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_required+=("$var")
    else
        print_success "✓ $var is configured"
    fi
done

for var in "${optional_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_optional+=("$var")
    else
        print_success "✓ $var is configured"
    fi
done

echo ""
echo "🎉 Setup Complete!"
echo "=================="

if [ ${#missing_required[@]} -eq 0 ]; then
    print_success "All required environment variables are configured"
else
    print_error "Missing required environment variables:"
    for var in "${missing_required[@]}"; do
        echo "  - $var"
    done
    echo ""
    echo "Please edit .env file and run this script again"
    exit 1
fi

if [ ${#missing_optional[@]} -gt 0 ]; then
    print_warning "Optional services not configured:"
    for var in "${missing_optional[@]}"; do
        echo "  - $var"
    done
    echo ""
    echo "These services will be available when you configure them in .env"
fi

echo ""
echo "🚀 How to Launch Claude Desktop with MCP:"
echo "=========================================="
echo ""
echo "Option 1: Use the launcher script"
echo "  $claude_launcher"
echo ""
echo "Option 2: Use the alias (after restarting terminal)"
echo "  claude-mcp"
echo ""
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Option 3: Use the macOS application"
    echo "  Open 'Claude MCP' from Applications folder"
    echo ""
fi
echo "Option 4: Manual launch"
echo "  source $claude_env_script"
echo "  open -a 'Claude'"
echo ""
echo "🔧 Troubleshooting:"
echo "==================="
echo "- Check Claude Desktop logs if servers don't connect"
echo "- Verify services are running (Qdrant: docker run -p 6333:6333 qdrant/qdrant)"
echo "- Test MCP servers individually with MCP Inspector"
echo "- Review configuration: claude-config"
echo ""
echo "📚 Documentation: README.md, FEATURES.md, mcp-servers-setup-guide.md"
