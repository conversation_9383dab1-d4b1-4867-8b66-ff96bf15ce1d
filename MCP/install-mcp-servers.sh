#!/bin/bash

# MCP Servers Installation Script
# This script installs all the MCP servers defined in augment_mcp_config.json

set -e  # Exit on any error

echo "🚀 Installing MCP Servers for PaceySpace..."
echo "=============================================="

# Check if uv/uvx is installed
if ! command -v uvx &> /dev/null; then
    echo "❌ uvx not found. Installing uv..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source ~/.bashrc || source ~/.zshrc || true
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm not found. Please install Node.js and npm first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Install Python-based MCP servers
echo "📦 Installing Python-based MCP servers..."
echo "----------------------------------------"

servers=(
    "mcp-server-qdrant"
    "mcp-server-brave-search" 
    "mcp-server-fetch"
    "mcp-server-filesystem"
    "mcp-server-github"
    "mcp-server-aws"
    "mcp-server-aws-cli"
    "mcp-server-1password"
    "mcp-server-vault"
    "mcp-server-bitwarden"
)

for server in "${servers[@]}"; do
    echo "Installing $server..."
    if uvx install "$server"; then
        echo "✅ $server installed successfully"
    else
        echo "⚠️  Failed to install $server (may not exist yet)"
    fi
done

echo ""

# Install Node.js-based MCP servers
echo "📦 Installing Node.js-based MCP servers..."
echo "----------------------------------------"

node_servers=(
    "context7-mcp"
    "@modelcontextprotocol/inspector"
)

for server in "${node_servers[@]}"; do
    echo "Installing $server..."
    if npm install -g "$server"; then
        echo "✅ $server installed successfully"
    else
        echo "⚠️  Failed to install $server"
    fi
done

echo ""

# Install additional tools
echo "🔧 Installing additional tools..."
echo "--------------------------------"

echo "Installing MCP Inspector for testing..."
if npm install -g @modelcontextprotocol/inspector; then
    echo "✅ MCP Inspector installed"
else
    echo "⚠️  Failed to install MCP Inspector"
fi

echo ""

# Check AWS CLI
echo "🔍 Checking AWS CLI..."
echo "---------------------"
if command -v aws &> /dev/null; then
    echo "✅ AWS CLI is installed"
    aws --version
else
    echo "⚠️  AWS CLI not found. Install it for AWS MCP servers:"
    echo "   macOS: brew install awscli"
    echo "   Linux: apt-get install awscli"
    echo "   Windows: https://aws.amazon.com/cli/"
fi

echo ""

# Check Docker for Qdrant
echo "🐳 Checking Docker for Qdrant..."
echo "--------------------------------"
if command -v docker &> /dev/null; then
    echo "✅ Docker is installed"
    echo "To start Qdrant locally:"
    echo "   docker run -p 6333:6333 qdrant/qdrant"
else
    echo "⚠️  Docker not found. Install it to run Qdrant locally:"
    echo "   Visit: https://docker.com/get-started"
fi

echo ""

# Setup instructions
echo "📋 Next Steps:"
echo "=============="
echo "1. Copy .env.template to .env and fill in your credentials:"
echo "   cp MCP/.env.template MCP/.env"
echo ""
echo "2. Edit MCP/.env with your actual API keys and tokens"
echo ""
echo "3. Choose your MCP configuration:"
echo "   - augment_mcp_config.json (with hardcoded placeholders)"
echo "   - augment_mcp_config_secure.json (uses environment variables)"
echo ""
echo "4. Copy your chosen config to Claude Desktop:"
echo "   cp MCP/augment_mcp_config_secure.json ~/Library/Application\\ Support/Claude/claude_desktop_config.json"
echo ""
echo "5. Start required services:"
echo "   - Qdrant: docker run -p 6333:6333 qdrant/qdrant"
echo "   - Vault (if using): vault server -dev"
echo ""
echo "6. Test your configuration:"
echo "   mcp-inspector uvx mcp-server-github"
echo ""
echo "7. Restart Claude Desktop to load the new configuration"
echo ""

echo "🎉 Installation complete!"
echo ""
echo "📚 For detailed setup instructions, see:"
echo "   - MCP/mcp-servers-setup-guide.md"
echo "   - MCP/.env.template"
echo ""
echo "🔒 Security reminder:"
echo "   - Never commit .env files to version control"
echo "   - Use different credentials for different environments"
echo "   - Regularly rotate your API keys and tokens"
