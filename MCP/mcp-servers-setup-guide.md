# MCP Servers Setup Guide

This guide covers the setup and configuration of all MCP servers in your `augment_mcp_config.json` file.

## Prerequisites

Before setting up these MCP servers, ensure you have the following installed:

```bash
# Install uv/uvx for Python package management
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install Node.js and npm (for some MCP servers)
# Visit https://nodejs.org/ or use your package manager

# Install AWS CLI (for AWS MCP servers)
# Visit https://aws.amazon.com/cli/ or use your package manager
```

## Server Configurations

### 1. Qdrant Vector Database
**Purpose**: Semantic memory storage and retrieval

**Setup**:
```bash
# Start local Qdrant instance
docker run -p 6333:6333 qdrant/qdrant

# Or install Qdrant locally
# Visit https://qdrant.tech/documentation/quick-start/
```

**Environment Variables**:
- `QDRANT_URL`: http://localhost:6333 (for local) or your Qdrant cloud URL
- `COLLECTION_NAME`: augment-memories (or your preferred collection name)
- `EMBEDDING_MODEL`: sentence-transformers/all-MiniLM-L6-v2

### 2. Brave Search
**Purpose**: Web search capabilities

**Setup**:
```bash
uvx install mcp-server-brave-search
```

**Required**:
- Get API key from https://api.search.brave.com/
- Replace `YOUR_BRAVE_API_KEY_HERE` with your actual API key

### 3. Context7 (Upstash Vector)
**Purpose**: Advanced context management with vector storage

**Setup**:
```bash
npm install -g context7-mcp
```

**Required**:
- Create account at https://upstash.com/
- Create a vector database
- Replace placeholders with your Upstash credentials

### 4. GitHub Integration
**Purpose**: GitHub repository management, issues, PRs, etc.

**Setup**:
```bash
uvx install mcp-server-github
```

**Required**:
- Create GitHub Personal Access Token at https://github.com/settings/tokens
- Recommended scopes: `repo`, `read:org`, `read:user`, `read:project`
- Replace `YOUR_GITHUB_TOKEN_HERE` with your token

### 5. AWS Services
**Purpose**: EC2, S3, Lambda, IAM, CloudFormation, RDS, ECS management

**Setup Option 1 - Direct AWS MCP Server**:
```bash
uvx install mcp-server-aws
```

**Setup Option 2 - AWS CLI MCP Server**:
```bash
uvx install mcp-server-aws-cli
```

**Required**:
- AWS account with programmatic access
- Create IAM user with appropriate permissions
- Configure AWS credentials (see AWS Configuration section below)

### 6. Secrets Management

#### Option A: 1Password (Recommended for teams)
**Setup**:
```bash
uvx install mcp-server-1password
```

**Required**:
- 1Password Business account
- Create service account at https://my.1password.com/
- Replace `YOUR_1PASSWORD_SERVICE_ACCOUNT_TOKEN` with your token

#### Option B: HashiCorp Vault (Self-hosted)
**Setup**:
```bash
uvx install mcp-server-vault
```

**Required**:
- Vault server running (local or remote)
- Vault token with appropriate permissions
- Update `VAULT_ADDR` and `VAULT_TOKEN`

#### Option C: Bitwarden (Open source alternative)
**Setup**:
```bash
uvx install mcp-server-bitwarden
```

**Required**:
- Bitwarden account
- API credentials from Bitwarden web vault
- Replace placeholders with your Bitwarden credentials

## AWS Configuration

### Method 1: Environment Variables (in MCP config)
Update the AWS sections in your MCP config with:
```json
"env": {
  "AWS_ACCESS_KEY_ID": "your-access-key-id",
  "AWS_SECRET_ACCESS_KEY": "your-secret-access-key",
  "AWS_DEFAULT_REGION": "us-east-1"
}
```

### Method 2: AWS Profiles (Recommended)
```bash
# Configure AWS CLI
aws configure

# Or create specific profile
aws configure --profile mcp-profile
```

Then update MCP config to use profile:
```json
"env": {
  "AWS_PROFILE": "mcp-profile",
  "AWS_DEFAULT_REGION": "us-east-1"
}
```

### Method 3: IAM Roles (For EC2 instances)
If running on EC2, attach an IAM role with necessary permissions.

## Security Best Practices

### 1. Environment Variables
Create a `.env` file for sensitive values:
```bash
# .env file (add to .gitignore)
GITHUB_TOKEN=ghp_xxxxxxxxxxxx
AWS_ACCESS_KEY_ID=AKIAXXXXXXXXXXXXXXXX
AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
BRAVE_API_KEY=BSAxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OP_SERVICE_ACCOUNT_TOKEN=ops_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 2. Use Secrets Management
Instead of hardcoding secrets, use one of the secrets management MCP servers to retrieve them dynamically.

### 3. Principle of Least Privilege
- Create dedicated IAM users/roles with minimal required permissions
- Use separate API keys for different environments
- Regularly rotate credentials

## Installation Commands

Run these commands to install all MCP servers:

```bash
# Core servers
uvx install mcp-server-qdrant
uvx install mcp-server-brave-search
uvx install mcp-server-fetch
uvx install mcp-server-filesystem

# GitHub integration
uvx install mcp-server-github

# AWS integration
uvx install mcp-server-aws
uvx install mcp-server-aws-cli

# Secrets management (choose one or more)
uvx install mcp-server-1password
uvx install mcp-server-vault
uvx install mcp-server-bitwarden

# Context management
npm install -g context7-mcp
```

## Testing Your Configuration

1. **Validate JSON syntax**:
```bash
cat MCP/augment_mcp_config.json | jq .
```

2. **Test individual servers**:
```bash
# Test Qdrant connection
curl http://localhost:6333/healthz

# Test AWS credentials
aws sts get-caller-identity

# Test GitHub token
curl -H "Authorization: token YOUR_GITHUB_TOKEN" https://api.github.com/user
```

3. **Use MCP Inspector** for interactive testing:
```bash
# Install MCP Inspector
npm install -g @modelcontextprotocol/inspector

# Test a server
mcp-inspector uvx mcp-server-github
```

## Next Steps

1. Replace all placeholder values with your actual credentials
2. Choose your preferred secrets management solution
3. Test each server individually before using the full configuration
4. Consider using environment variables or external secrets management for production use
5. Restart your MCP client after updating the configuration

## Troubleshooting

- **Connection errors**: Check that services (Qdrant, Vault, etc.) are running
- **Authentication errors**: Verify API keys and tokens are correct and have proper permissions
- **Permission errors**: Ensure IAM users/roles have necessary AWS permissions
- **Installation errors**: Make sure uvx and npm are properly installed and in your PATH
