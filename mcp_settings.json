{"mcpServers": {"qdrant": {"command": "uvx", "args": ["mcp-server-qdrant"], "env": {"QDRANT_URL": "https://95a994e1-b2a5-477b-bf8f-a38c1e79648c.us-west-2-0.aws.cloud.qdrant.io", "QDRANT_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.IF7mNv1EXEZ8xNSlgB4OkRQe8W07t0f_fDWVKblV6fI", "COLLECTION_NAME": "first", "EMBEDDING_MODEL": "mixedbread-ai/mxbai-embed-large-v1"}}, "backblaze": {"command": "node", "args": ["tools/backblaze-mcp/server.js"], "env": {"B2_ACCOUNT_ID": "${B2_ACCOUNT_ID}", "B2_APPLICATION_KEY": "${B2_APPLICATION_KEY}", "B2_BUCKET_ID": "${B2_BUCKET_ID}"}, "description": "Local Backblaze B2 MCP server scaffold. Requires environment variables to be set (B2_ACCOUNT_ID, B2_APPLICATION_KEY, B2_BUCKET_ID). Start command: `node tools/backblaze-mcp/server.js`"}}}