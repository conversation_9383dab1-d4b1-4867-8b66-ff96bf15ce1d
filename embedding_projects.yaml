projects:
- agent: claude
  exclude_patterns:
  - '*.pyc'
  - __pycache__
  - .git
  - node_modules
  - .venv
  model: dengcao/Qwen3-Embedding-8B:Q4_K_M
  name: MCP Servers
  path: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP/MCP
  priority: high
  sharing: shared
  tags:
  - mcp
  - infrastructure
  - embedding
  type: library
- agent: claude
  exclude_patterns:
  - node_modules
  - dist
  - build
  model: dengcao/Qwen3-Embedding-4B:Q4_K_M
  name: PaceySpace Projects
  path: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/PaceySpace-Projects
  priority: high
  sharing: shared
  tags:
  - web
  - frontend
  - react
  type: web
- agent: claude
  exclude_patterns:
  - .terraform
  - '*.tfstate'
  model: dengcao/Qwen3-Embedding-4B:Q4_K_M
  name: PaceySpace Labs
  path: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/PaceySpace-Labs
  priority: medium
  sharing: project-specific
  tags:
  - devops
  - infrastructure
  - automation
  type: devops
- agent: claude
  model: dengcao/Qwen3-Embedding-0.6B:Q8_0
  name: Client Project Alpha
  path: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/Client-Alpha
  priority: medium
  sharing: private
  tags:
  - client
  - confidential
  type: client
