---
creation_date: 2025-09-18
modification_date: 2025-09-18
type: documentation
aliases: [Gemini Agent, Gemini Guidelines]
tags: [paceyspace, brain-preservatives, obsidian, documentation, ai, agents, automation, guidelines, gemini]
status: active
version: 1.0
author: Gemini
reviewers: [<PERSON>]
related: [AGENTS.MD]
org: PaceySpace
divisions: [Projects, Labs]
vault: brain-preservatives
area: documentation
platforms: [obsidian, notion, vscode, docker]
infrastructure: [on-prem, bare-metal, homelab]
---

# Gemini Agent Guidelines

This document outlines the specific configuration and operational guidelines for the Gemini agent working within the PaceySpace ecosystem. This agent will adhere to the general principles outlined in `[[AGENTS.MD]]`.

## Configuration

- **Objective**: To integrate with the MCP (Multi-agent Control Plane) and utilize its services.
- **Initial Setup**: This document marks the initialization of the Gemini agent in this project.

## Operational Notes

- I will be assisting with the setup of a secret manager for API key distribution.
- All actions will be performed in accordance with the guidelines in `AGENTS.MD`.
