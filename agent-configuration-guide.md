# Multi-Agent Configuration Guide

## Overview
This guide provides configuration settings to disable approval prompts for roocodes and cline, enabling streamlined multi-agent workflows on your Proxmox server.

## Cline Configuration ✅ COMPLETED

**File**: `MCP/cline_mcp_settings.json`

**Changes Made**:
- Updated `autoApprove` from `[]` to `["*"]` to auto-approve all MCP tool calls
- This eliminates approval prompts for MCP server interactions

**Current Configuration**:
```json
{
  "mcpServers": {
    "github.com/NightTrek/Ollama-mcp": {
      "command": "node",
      "args": ["/Users/<USER>/Documents/Cline/MCP/ollama-mcp/build/index.js"],
      "env": {
        "OLLAMA_HOST": "http://127.0.0.1:11434"
      },
      "disabled": false,
      "autoApprove": ["*"]
    }
  }
}
```

## Roocodes Configuration

**Location Options** (check these locations):

### 1. VS Code User Settings
Add to your VS Code `settings.json`:
```json
{
  "roocodes.autoApprove": true,
  "roocodes.confirmBeforeExecution": false,
  "roocodes.requireApproval": false,
  "roocodes.skipConfirmation": true
}
```

### 2. Workspace Settings
Create `.vscode/settings.json` in your project root:
```json
{
  "roocodes.autoApprove": true,
  "roocodes.confirmBeforeExecution": false,
  "roocodes.requireApproval": false,
  "roocodes.skipConfirmation": true
}
```

### 3. Extension Configuration File
Some extensions use dedicated config files. Check for:
- `.roocodes.json`
- `.roocodes.config.json`
- `roocodes.config.js`

Example configuration:
```json
{
  "autoApprove": true,
  "confirmBeforeExecution": false,
  "requireApproval": false,
  "skipConfirmation": true,
  "safeMode": false
}
```

## Multi-Agent Workflow Recommendations

### 1. Environment Isolation
```bash
# Use Docker containers for each agent
docker run -d --name agent1 --network agent-network your-agent-image
docker run -d --name agent2 --network agent-network your-agent-image
```

### 2. Shared State Management
- **Redis**: For real-time state sharing
- **Database**: PostgreSQL/MySQL for persistent state
- **Message Queue**: RabbitMQ/Apache Kafka for task coordination

### 3. Resource Management
```yaml
# docker-compose.yml for multi-agent setup
version: '3.8'
services:
  agent-coordinator:
    image: your-coordinator-image
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - postgres
  
  agent-worker-1:
    image: your-agent-image
    environment:
      - AGENT_ID=worker-1
      - COORDINATOR_URL=http://agent-coordinator:8080
    
  agent-worker-2:
    image: your-agent-image
    environment:
      - AGENT_ID=worker-2
      - COORDINATOR_URL=http://agent-coordinator:8080
  
  redis:
    image: redis:alpine
    
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=agents
      - POSTGRES_USER=agent_user
      - POSTGRES_PASSWORD=secure_password
```

### 4. Communication Patterns
- **Event-driven**: Use webhooks/events for loose coupling
- **API Gateway**: Central routing for agent communications
- **Load Balancing**: Distribute workload across agents

### 5. Monitoring & Logging
```bash
# Centralized logging with ELK stack
docker run -d --name elasticsearch elasticsearch:8.11.0
docker run -d --name kibana --link elasticsearch kibana:8.11.0
docker run -d --name logstash --link elasticsearch logstash:8.11.0
```

### 6. Security Considerations
- Use API keys for agent authentication
- Implement rate limiting
- Network segmentation with Docker networks
- Regular security updates

### 7. Configuration Management
```bash
# Use environment variables for configuration
export AGENT_AUTO_APPROVE=true
export AGENT_SKIP_CONFIRMATION=true
export AGENT_SAFE_MODE=false
```

## Next Steps

1. **Apply roocodes configuration** using one of the methods above
2. **Test both agents** with simple tasks to verify auto-approval works
3. **Set up monitoring** to track agent performance and errors
4. **Implement coordination** if agents need to work together
5. **Create backup/recovery** procedures for your Proxmox environment

## Troubleshooting

### If roocodes still asks for approval:
1. Check VS Code extension settings
2. Restart VS Code after configuration changes
3. Verify the extension is using the correct config file
4. Check extension documentation for specific setting names

### If cline still asks for approval:
1. Restart the cline application
2. Verify the MCP settings file is in the correct location
3. Check cline logs for configuration errors

## Additional Tools for Multi-Agent Workflows

- **Ansible**: For infrastructure automation
- **Terraform**: For infrastructure as code
- **Prometheus**: For metrics collection
- **Grafana**: For visualization and dashboards
- **Consul**: For service discovery
- **Vault**: For secrets management
