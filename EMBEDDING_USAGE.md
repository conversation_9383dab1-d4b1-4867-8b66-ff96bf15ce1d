# Multi-Agent Embedding System Usage Guide

## Overview

This system provides stable, shared context across multiple AI agents (<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, etc.) using Qdrant vector database with intelligent collection strategy.

## Quick Start

### 1. Basic Embedding
```bash
python3 embed_documents.py
```

Follow the prompts to:
- Choose embedding model (8B for quality, 4B for balance, 0.6B for speed)
- Select agent context
- Choose shared vs private storage
- Specify path to embed

### 2. Context Management
```bash
python3 shared_context_manager.py
```

Operations available:
- **search**: Find information across all agent contexts
- **conflicts**: Detect conflicting information between agents
- **consolidate**: Resolve conflicts into shared knowledge
- **stats**: View knowledge distribution statistics
- **sync**: Copy knowledge between agents

### 3. Strategy Overview
```bash
python3 qdrant_strategy.py
```

Shows the complete collection architecture and routing strategy.

## Collection Strategy

### Shared Collections (All Agents)
- `shared_knowledge_8b` - High-quality curated knowledge
- `shared_docs_4b` - Documentation accessible to all
- `shared_code_4b` - Shared codebase knowledge

### Agent-Specific Collections
- `{agent}_conversations_4b` - Private conversation history
- `{agent}_workspace_0_6b` - Fast workspace indexing

### Project Collections
- `paceyspace_projects_8b` - Web development context
- `paceyspace_labs_4b` - DevOps/Infrastructure context

### Model-Specific Collections
- `search_{model}` - Fast search collections per model

## Embedding Models

| Model | Size | Use Case | Performance |
|-------|------|----------|-------------|
| Qwen3-8B | 4.7GB | Critical docs, knowledge base | Highest quality |
| Qwen3-4B | 2.5GB | General purpose | Balanced |
| Qwen3-0.6B | 639MB | Large datasets, rapid indexing | Fastest |

## Benefits of Multi-Tenant Architecture

### 1. **Shared Knowledge Growth**
- Agents contribute to and benefit from collective knowledge
- Context builds continuously across sessions
- Reduced redundancy in embedding storage

### 2. **Flexible Isolation**
- Private collections for sensitive context
- Shared collections for collaborative knowledge
- Project-specific boundaries for organized context

### 3. **Cross-Model Compatibility**
- Metadata tracks embedding models used
- Search across collections created with different models
- Gradual migration between models

### 4. **Stable Performance**
- Hash-based deduplication prevents conflicts
- Timestamped entries for version tracking
- Intelligent collection routing based on content type

## Resource Management

### Recommended Usage by System Resources

**High-end System (32GB+ RAM):**
- Use 8B model for important documents
- 4B model for general use
- Enable shared collections for all content

**Mid-range System (16GB RAM):**
- Use 4B model primarily
- 8B model for critical knowledge only
- Balance shared vs private based on use case

**Lower-end System (8GB RAM):**
- Use 0.6B model for fast indexing
- 4B model sparingly for important content
- Focus on private collections to reduce search overhead

## Example Workflows

### 1. Setting up Project Context
```bash
# Embed project documentation
python3 embed_documents.py
# Choose: 4B model, claude agent, shared mode
# Path: /path/to/project/docs

# Embed codebase
python3 embed_documents.py
# Choose: 4B model, claude agent, shared mode
# Path: /path/to/project/src
```

### 2. Cross-Agent Knowledge Sharing
```bash
# Search across all agents for information
python3 shared_context_manager.py
# Choose: search
# Query: "authentication implementation"
# Agent: claude
```

### 3. Conflict Resolution
```bash
# Find conflicting information
python3 shared_context_manager.py
# Choose: conflicts
# Topic: "API rate limits"

# Consolidate conflicts
python3 shared_context_manager.py
# Choose: consolidate
# Topic: "API rate limits"
```

### 4. Knowledge Sync Between Agents
```bash
# Sync high-quality knowledge from one agent to another
python3 shared_context_manager.py
# Choose: sync
# Source: claude
# Target: auggie
```

## Environment Setup

### Required Environment Variables
```bash
export QDRANT_URL="http://localhost:6333"          # Qdrant server URL
export QDRANT_API_KEY="your-api-key"               # If using managed Qdrant
export COLLECTION_NAME="default"                   # Default collection
```

### Ollama Models
```bash
# Install required embedding models
ollama pull dengcao/Qwen3-Embedding-8B:Q4_K_M
ollama pull dengcao/Qwen3-Embedding-4B:Q4_K_M
ollama pull dengcao/Qwen3-Embedding-0.6B:Q8_0
```

### Python Dependencies
```bash
pip install qdrant-client ollama
```

## Monitoring and Maintenance

### View Current State
```bash
# Check collection statistics
python3 shared_context_manager.py
# Choose: stats

# View collection strategy
python3 qdrant_strategy.py
```

### Cleanup Operations
```bash
# Remove old collections (manual via Qdrant client)
# Monitor disk usage
# Periodic conflict resolution
```

## Integration with MCP Servers

This system integrates with the existing MCP infrastructure:

1. **Qdrant MCP Server** - Provides search/store tools for AI agents
2. **Collection Strategy** - Routes requests to appropriate collections
3. **Cross-Agent Search** - Enables knowledge sharing between different MCP clients

## Troubleshooting

### Common Issues

**"No embedding models found"**
- Install Ollama models: `ollama pull dengcao/Qwen3-Embedding-4B:Q4_K_M`

**"Collection creation failed"**
- Check Qdrant server is running: `curl http://localhost:6333/healthz`
- Verify permissions and disk space

**"Search returns no results"**
- Check if collections exist and have data
- Verify embedding model compatibility
- Try broader search terms

### Performance Optimization

1. **Use appropriate model for content size**
2. **Balance shared vs private collections**
3. **Regular conflict resolution**
4. **Monitor collection sizes**

This system provides a robust foundation for multi-agent AI collaboration with shared, evolving context that remains stable and consistent across different agents and sessions.