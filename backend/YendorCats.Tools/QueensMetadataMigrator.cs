using System.Text.Json;
using Microsoft.Extensions.Logging;
using YendorCats.API.Services;

namespace YendorCats.Tools
{
    /// <summary>
    /// Targeted migration tool to populate S3 metadata for queen images
    /// Handles S3 metadata size limits by truncating descriptions appropriately
    /// </summary>
    public class QueensMetadataMigrator
    {
        private readonly ILogger<QueensMetadataMigrator> _logger;
        private readonly IS3StorageService _s3StorageService;
        
        // S3 Metadata limits: 2KB per value, 8KB total per object
        private const int MAX_DESCRIPTION_LENGTH = 1800; // Leave room for other metadata
        private const int MAX_TOTAL_METADATA_SIZE = 7000; // Conservative limit

        public QueensMetadataMigrator(
            ILogger<QueensMetadataMigrator> logger,
            IS3StorageService s3StorageService)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
        }

        public async Task<MigrationResult> ExecuteQueensMigrationAsync(bool dryRun = true)
        {
            var result = new MigrationResult
            {
                StartTime = DateTime.UtcNow,
                IsDryRun = dryRun
            };

            try
            {
                _logger.LogInformation("Starting Queens metadata migration from cat-descriptions.json to S3. DryRun: {DryRun}", dryRun);

                // Load cat-descriptions.json
                var queensData = await LoadQueensDescriptionsAsync();
                if (queensData == null || queensData.Count == 0)
                {
                    result.ErrorMessage = "No queens data found in cat-descriptions.json";
                    return result;
                }

                _logger.LogInformation("Loaded {Count} queens from cat-descriptions.json", queensData.Count);

                // Get all S3 objects in queens/
                var s3Objects = await _s3StorageService.ListFilesAsync("queens/");
                var imageObjects = s3Objects.Where(obj => IsImageFile(obj.Key)).ToList();

                _logger.LogInformation("Found {Count} image objects in S3 queens/ folder", imageObjects.Count);

                // Process each S3 object
                foreach (var s3Object in imageObjects)
                {
                    result.ObjectsProcessed++;

                    try
                    {
                        var objectResult = await ProcessSingleQueenImageAsync(s3Object, queensData, dryRun);
                        if (objectResult.Updated)
                        {
                            result.ObjectsUpdated++;
                            result.UpdatedObjects.Add(objectResult);
                        }
                        else
                        {
                            result.ObjectsSkipped++;
                            if (!string.IsNullOrEmpty(objectResult.SkipReason))
                            {
                                result.SkipReasons.Add($"{s3Object.Key}: {objectResult.SkipReason}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing S3 object: {Key}", s3Object.Key);
                        result.Errors.Add($"{s3Object.Key}: {ex.Message}");
                    }
                }

                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
                result.Success = result.Errors.Count == 0;

                var summary = dryRun ? "DRY RUN completed" : "Migration completed";
                _logger.LogInformation("{Summary}: {Processed} objects processed, {Updated} updated, {Skipped} skipped, {Errors} errors",
                    summary, result.ObjectsProcessed, result.ObjectsUpdated, result.ObjectsSkipped, result.Errors.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fatal error during queens metadata migration");
                result.ErrorMessage = ex.Message;
                result.Success = false;
                result.EndTime = DateTime.UtcNow;
                return result;
            }
        }

        private async Task<Dictionary<string, QueenDescription>?> LoadQueensDescriptionsAsync()
        {
            var catDescPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "frontend", "resources", "cat-descriptions.json");
            
            try
            {
                if (!File.Exists(catDescPath))
                {
                    _logger.LogError("cat-descriptions.json not found at: {Path}", catDescPath);
                    return null;
                }

                var json = await File.ReadAllTextAsync(catDescPath);
                var rawData = JsonSerializer.Deserialize<CatDescriptionsFile>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return rawData?.Queens;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading cat-descriptions.json from {Path}", catDescPath);
                return null;
            }
        }

        private async Task<ObjectProcessResult> ProcessSingleQueenImageAsync(
            Amazon.S3.Model.S3Object s3Object,
            Dictionary<string, QueenDescription> queensData,
            bool dryRun)
        {
            var result = new ObjectProcessResult { S3Key = s3Object.Key };

            // Get current metadata
            var currentMetadata = await _s3StorageService.GetObjectMetadataDirectAsync(s3Object.Key);
            
            // Check if already has rich metadata (skip if so)
            if (HasRichQueenMetadata(currentMetadata))
            {
                result.SkipReason = "Already has rich metadata";
                return result;
            }

            // Extract probable queen name from various sources
            var queenName = ExtractQueenNameFromS3Object(s3Object.Key, currentMetadata);
            if (string.IsNullOrEmpty(queenName))
            {
                result.SkipReason = "Could not determine queen name";
                return result;
            }

            // Find matching queen data (case-insensitive)
            var matchingQueen = queensData.FirstOrDefault(kvp => 
                string.Equals(kvp.Key, queenName, StringComparison.OrdinalIgnoreCase));

            if (matchingQueen.Key == null)
            {
                result.SkipReason = $"No description found for queen '{queenName}'";
                return result;
            }

            // Build new metadata
            var newMetadata = BuildQueenMetadata(queenName, matchingQueen.Value, currentMetadata);

            // Validate metadata size
            if (!ValidateMetadataSize(newMetadata))
            {
                result.SkipReason = "Metadata would exceed S3 limits after truncation";
                return result;
            }

            result.QueenName = queenName;
            result.NewMetadata = newMetadata;

            if (!dryRun)
            {
                // Write to S3
                await _s3StorageService.UpdateObjectMetadataAsync(s3Object.Key, newMetadata);
                result.Updated = true;
                _logger.LogInformation("Updated S3 metadata for {Key} with queen data for '{Queen}'", 
                    s3Object.Key, queenName);
            }
            else
            {
                result.Updated = true; // Would be updated
                _logger.LogInformation("DRY RUN: Would update {Key} with metadata for queen '{Queen}'", 
                    s3Object.Key, queenName);
            }

            return result;
        }

        private string? ExtractQueenNameFromS3Object(string s3Key, Dictionary<string, string> currentMetadata)
        {
            // Priority 1: Check if current metadata has a real name (not fallback)
            var currentName = GetMetadataValue(currentMetadata, "cat-name", "name", "catname");
            if (!string.IsNullOrEmpty(currentName) && !IsFallbackName(currentName))
            {
                return CleanQueenName(currentName);
            }

            // Priority 2: Extract from S3 key structure
            var nameFromPath = ExtractNameFromS3Path(s3Key);
            if (!string.IsNullOrEmpty(nameFromPath) && !IsFallbackName(nameFromPath))
            {
                return CleanQueenName(nameFromPath);
            }

            // Priority 3: Extract from filename
            var fileName = Path.GetFileNameWithoutExtension(s3Key);
            var nameFromFile = ExtractNameFromFileName(fileName);
            if (!string.IsNullOrEmpty(nameFromFile) && !IsFallbackName(nameFromFile))
            {
                return CleanQueenName(nameFromFile);
            }

            return null; // Could not determine
        }

        private string? ExtractNameFromS3Path(string s3Key)
        {
            // Handle patterns like:
            // queens/Indy/Indy-8-010123-1.png -> "Indy"
            // queens/Ghostgums-4-030324-1.png -> "Ghostgums"
            
            var relativePath = _s3StorageService.RemoveKeyPrefix(s3Key);
            var parts = relativePath.Split('/');

            if (parts.Length == 3 && parts[1] != "General")
            {
                // queens/[QueenName]/filename.ext
                return parts[1];
            }
            
            if (parts.Length == 2)
            {
                // queens/Filename-with-queenname.ext
                var fileName = Path.GetFileNameWithoutExtension(parts[1]);
                return ExtractNameFromFileName(fileName);
            }

            return null;
        }

        private string? ExtractNameFromFileName(string fileName)
        {
            // Remove common prefixes
            var cleaned = fileName
                .Replace("TESTING-", "")
                .Replace("IMG_", "")
                .Replace("DSC_", "");

            // Split and find the first meaningful part
            var parts = cleaned.Split(new[] { '-', '_', ' ' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var part in parts)
            {
                // Skip numeric parts, dates, etc.
                if (part.All(char.IsDigit) || part.Length < 3)
                    continue;

                // Skip date patterns like 030324, 010123
                if (System.Text.RegularExpressions.Regex.IsMatch(part, @"^\d{6}$"))
                    continue;

                return part;
            }

            return null;
        }

        private bool IsFallbackName(string name)
        {
            if (string.IsNullOrEmpty(name)) return true;
            
            var lower = name.ToLower();
            return lower.Contains("beautiful") || 
                   lower.Contains("queen") || 
                   lower.StartsWith("img_") || 
                   lower.StartsWith("dsc_") ||
                   lower == "general" ||
                   lower == "cat1" ||
                   lower == "unknown";
        }

        private string CleanQueenName(string name)
        {
            return name?.Trim()
                .Replace("_", " ")
                .Replace("-", " ")
                .Replace("  ", " ")
                .Split(' ')
                .Select(word => word.Length > 0 ? char.ToUpper(word[0]) + word.Substring(1).ToLower() : word)
                .Where(word => !string.IsNullOrWhiteSpace(word))
                .FirstOrDefault() ?? "Unknown";
        }

        private Dictionary<string, string> BuildQueenMetadata(
            string queenName, 
            QueenDescription queenDesc, 
            Dictionary<string, string> currentMetadata)
        {
            var metadata = new Dictionary<string, string>();

            // Core fields
            metadata["cat-name"] = queenName;
            metadata["breed"] = "Maine Coon";
            metadata["gender"] = "F";
            metadata["category"] = "queens";

            // Extract information from description
            var desc = queenDesc.Description ?? "";
            
            // Truncate description if needed
            var processedDesc = TruncateDescription(desc);
            if (!string.IsNullOrEmpty(processedDesc))
            {
                metadata["description"] = processedDesc;
            }

            // Extract structured data from description text
            ExtractInfoFromDescription(desc, metadata);

            // Preserve some existing metadata if valuable
            PreserveExistingMetadata(currentMetadata, metadata);

            // Add migration tracking
            metadata["metadata-source"] = "cat-descriptions.json";
            metadata["migrated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

            return metadata;
        }

        private string TruncateDescription(string description)
        {
            if (string.IsNullOrEmpty(description))
                return "";

            if (description.Length <= MAX_DESCRIPTION_LENGTH)
                return description;

            // Truncate at sentence boundary if possible
            var truncated = description.Substring(0, MAX_DESCRIPTION_LENGTH);
            var lastSentenceEnd = Math.Max(
                truncated.LastIndexOf('.'),
                Math.Max(truncated.LastIndexOf('!'), truncated.LastIndexOf('?'))
            );

            if (lastSentenceEnd > MAX_DESCRIPTION_LENGTH / 2)
            {
                return truncated.Substring(0, lastSentenceEnd + 1).Trim();
            }

            // Truncate at word boundary
            var lastSpace = truncated.LastIndexOf(' ');
            if (lastSpace > MAX_DESCRIPTION_LENGTH / 2)
            {
                return truncated.Substring(0, lastSpace).Trim() + "...";
            }

            return truncated.Trim() + "...";
        }

        private void ExtractInfoFromDescription(string description, Dictionary<string, string> metadata)
        {
            if (string.IsNullOrEmpty(description)) return;

            var lower = description.ToLower();

            // Extract color/pattern information
            var colors = new[] { "black", "blue", "red", "silver", "smoke", "tortie", "tabby", "white" };
            var foundColors = colors.Where(color => lower.Contains(color)).ToList();
            if (foundColors.Any())
            {
                metadata["color"] = string.Join(" ", foundColors);
            }

            // Extract age information  
            var ageMatches = System.Text.RegularExpressions.Regex.Matches(description, @"(\d+)\s+(years?|months?)\s+old");
            if (ageMatches.Count > 0)
            {
                metadata["age"] = ageMatches[0].Groups[0].Value;
            }

            // Extract breeding status
            if (lower.Contains("retired") || lower.Contains("retiring"))
                metadata["breeding-status"] = "retired";
            else if (lower.Contains("bred") || lower.Contains("breeding") || lower.Contains("litter"))
                metadata["breeding-status"] = "active";

            // Extract lineage info
            if (lower.Contains("daughter") || lower.Contains("granddaughter"))
            {
                // Try to extract parent names
                var parentMatch = System.Text.RegularExpressions.Regex.Match(description, @"(mum|dad|father|mother) is (\w+)", RegexOptions.IgnoreCase);
                if (parentMatch.Success)
                {
                    metadata["bloodline"] = $"Descendant of {parentMatch.Groups[2].Value}";
                }
            }
        }

        private void PreserveExistingMetadata(Dictionary<string, string> current, Dictionary<string, string> newMeta)
        {
            // Preserve any useful existing metadata that we don't want to overwrite
            var preserveKeys = new[] { "date-taken", "photo-date", "file-format", "last-modified" };
            
            foreach (var key in preserveKeys)
            {
                var value = GetMetadataValue(current, key);
                if (!string.IsNullOrEmpty(value) && !newMeta.ContainsKey(key))
                {
                    newMeta[key] = value;
                }
            }
        }

        private bool HasRichQueenMetadata(Dictionary<string, string> metadata)
        {
            if (metadata == null || metadata.Count == 0)
                return false;

            // Check for indicators that this already has real metadata
            var catName = GetMetadataValue(metadata, "cat-name", "name", "catname");
            if (string.IsNullOrEmpty(catName) || IsFallbackName(catName))
                return false;

            // If it has both a real name and description, consider it rich
            var description = GetMetadataValue(metadata, "description");
            return !string.IsNullOrEmpty(description) && 
                   !description.Contains("Beautiful") && 
                   description.Length > 50;
        }

        private bool ValidateMetadataSize(Dictionary<string, string> metadata)
        {
            var totalSize = metadata.Sum(kvp => kvp.Key.Length + kvp.Value.Length);
            return totalSize <= MAX_TOTAL_METADATA_SIZE;
        }

        private string? GetMetadataValue(Dictionary<string, string> metadata, params string[] possibleKeys)
        {
            foreach (var key in possibleKeys)
            {
                if (metadata.TryGetValue(key, out var value) && !string.IsNullOrWhiteSpace(value))
                    return value.Trim();

                var kvp = metadata.FirstOrDefault(m => string.Equals(m.Key, key, StringComparison.OrdinalIgnoreCase));
                if (!string.IsNullOrEmpty(kvp.Key) && !string.IsNullOrWhiteSpace(kvp.Value))
                    return kvp.Value.Trim();
            }
            return null;
        }

        private bool IsImageFile(string key)
        {
            var extension = Path.GetExtension(key).ToLowerInvariant();
            return new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp" }.Contains(extension);
        }
    }

    public class CatDescriptionsFile
    {
        public Dictionary<string, QueenDescription> Queens { get; set; } = new();
    }

    public class QueenDescription
    {
        public string Description { get; set; } = string.Empty;
    }

    public class MigrationResult
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public bool IsDryRun { get; set; }
        public string? ErrorMessage { get; set; }
        
        public int ObjectsProcessed { get; set; }
        public int ObjectsUpdated { get; set; }
        public int ObjectsSkipped { get; set; }
        
        public List<string> Errors { get; set; } = new();
        public List<string> SkipReasons { get; set; } = new();
        public List<ObjectProcessResult> UpdatedObjects { get; set; } = new();
    }

    public class ObjectProcessResult
    {
        public string S3Key { get; set; } = string.Empty;
        public bool Updated { get; set; }
        public string? SkipReason { get; set; }
        public string? QueenName { get; set; }
        public Dictionary<string, string> NewMetadata { get; set; } = new();
    }
}
