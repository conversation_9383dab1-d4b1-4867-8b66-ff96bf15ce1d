using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using YendorCats.API.Configuration;
using YendorCats.API.Services;

namespace YendorCats.API.Services.Migration
{
    /// <summary>
    /// One-off migration tool to populate S3 metadata from cat-descriptions.json
    /// This ensures the rich metadata travels with images when they move around
    /// </summary>
    public interface ICatDescToS3Migrator
    {
        /// <summary>
        /// Execute the migration from cat-descriptions.json to S3 metadata
        /// </summary>
        /// <param name="dryRun">If true, only log what would be done without making changes</param>
        /// <param name="category">Specific category to migrate, or null for all</param>
        /// <returns>Migration result summary</returns>
        Task<MigrationResult> ExecuteMigrationAsync(bool dryRun = true, string? category = null);
    }

    public class CatDescToS3Migrator : ICatDescToS3Migrator
    {
        private readonly ILogger<CatDescToS3Migrator> _logger;
        private readonly IS3StorageService _s3StorageService;
        private readonly StorageProviderConfiguration _storageConfig;
        private readonly string _catDescriptionsPath;

        public CatDescToS3Migrator(
            ILogger<CatDescToS3Migrator> logger,
            IS3StorageService s3StorageService,
            IOptions<StorageProviderConfiguration> storageConfig)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
            _storageConfig = storageConfig.Value;
            _catDescriptionsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "frontend", "resources", "cat-descriptions.json");
        }

        public async Task<MigrationResult> ExecuteMigrationAsync(bool dryRun = true, string? category = null)
        {
            var result = new MigrationResult
            {
                StartTime = DateTime.UtcNow,
                IsDryRun = dryRun,
                Category = category
            };

            try
            {
                _logger.LogInformation("Starting cat-descriptions.json → S3 metadata migration. DryRun: {DryRun}, Category: {Category}", 
                    dryRun, category ?? "all");

                // Step 1: Load cat-descriptions.json
                var catDescriptions = await LoadCatDescriptionsAsync();
                if (catDescriptions == null)
                {
                    result.ErrorMessage = "Failed to load cat-descriptions.json";
                    result.Success = false;
                    return result;
                }

                // Step 2: Process each category
                var categoriesToProcess = string.IsNullOrEmpty(category) 
                    ? catDescriptions.Keys.ToList() 
                    : new List<string> { category };

                foreach (var cat in categoriesToProcess)
                {
                    if (!catDescriptions.ContainsKey(cat))
                    {
                        _logger.LogWarning("Category {Category} not found in cat-descriptions.json", cat);
                        continue;
                    }

                    var categoryResult = await ProcessCategoryAsync(cat, catDescriptions[cat], dryRun);
                    result.CategoriesProcessed++;
                    result.ObjectsProcessed += categoryResult.ObjectsProcessed;
                    result.ObjectsUpdated += categoryResult.ObjectsUpdated;
                    result.ObjectsSkipped += categoryResult.ObjectsSkipped;
                    result.Errors.AddRange(categoryResult.Errors);
                    result.CategoryResults[cat] = categoryResult;

                    _logger.LogInformation("Category {Category}: {Processed} processed, {Updated} updated, {Skipped} skipped", 
                        cat, categoryResult.ObjectsProcessed, categoryResult.ObjectsUpdated, categoryResult.ObjectsSkipped);
                }

                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
                result.Success = result.Errors.Count == 0;

                var summary = dryRun ? "DRY RUN completed" : "Migration completed";
                _logger.LogInformation("{Summary}: {Categories} categories, {Processed} objects processed, {Updated} updated, {Errors} errors",
                    summary, result.CategoriesProcessed, result.ObjectsProcessed, result.ObjectsUpdated, result.Errors.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fatal error during cat-descriptions migration");
                result.ErrorMessage = ex.Message;
                result.Success = false;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
                return result;
            }
        }

        private async Task<Dictionary<string, Dictionary<string, CatDescriptionEntry>>?> LoadCatDescriptionsAsync()
        {
            try
            {
                if (!File.Exists(_catDescriptionsPath))
                {
                    _logger.LogError("cat-descriptions.json not found at: {Path}", _catDescriptionsPath);
                    return null;
                }

                var json = await File.ReadAllTextAsync(_catDescriptionsPath);
                var rawData = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, CatDescriptionEntry>>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                _logger.LogInformation("Loaded cat-descriptions.json with {Categories} categories", rawData?.Count ?? 0);
                return rawData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading cat-descriptions.json from {Path}", _catDescriptionsPath);
                return null;
            }
        }

        private async Task<CategoryMigrationResult> ProcessCategoryAsync(
            string category, 
            Dictionary<string, CatDescriptionEntry> catDescriptions, 
            bool dryRun)
        {
            var result = new CategoryMigrationResult { Category = category };

            try
            {
                // Get all S3 objects in this category
                var s3Objects = await _s3StorageService.ListFilesAsync($"{category}/");
                var imageObjects = s3Objects.Where(obj => IsImageFile(obj.Key)).ToList();

                _logger.LogDebug("Found {Count} image objects in S3 category {Category}", imageObjects.Count, category);

                foreach (var s3Object in imageObjects)
                {
                    result.ObjectsProcessed++;

                    try
                    {
                        // Extract cat name from S3 key
                        var catName = ExtractCatNameFromS3Key(s3Object.Key);
                        if (string.IsNullOrEmpty(catName))
                        {
                            _logger.LogWarning("Could not extract cat name from S3 key: {Key}", s3Object.Key);
                            result.ObjectsSkipped++;
                            continue;
                        }

                        // Find matching description (case-insensitive)
                        var matchingDesc = catDescriptions
                            .FirstOrDefault(kvp => string.Equals(kvp.Key, catName, StringComparison.OrdinalIgnoreCase));

                        if (matchingDesc.Key == null)
                        {
                            _logger.LogDebug("No description found for cat {CatName} in {Category}", catName, category);
                            result.ObjectsSkipped++;
                            continue;
                        }

                        // Check if object already has custom metadata
                        var existingMetadata = await _s3StorageService.GetObjectMetadataDirectAsync(s3Object.Key);
                        if (HasRichMetadata(existingMetadata))
                        {
                            _logger.LogDebug("S3 object {Key} already has rich metadata, skipping", s3Object.Key);
                            result.ObjectsSkipped++;
                            continue;
                        }

                        // Prepare metadata to write
                        var metadataToWrite = PrepareS3Metadata(catName, matchingDesc.Value, category);

                        if (!dryRun)
                        {
                            // Write metadata to S3
                            var success = await WriteMetadataToS3Async(s3Object.Key, metadataToWrite);
                            if (success)
                            {
                                result.ObjectsUpdated++;
                                _logger.LogInformation("Updated S3 metadata for {Key}: {CatName}", s3Object.Key, catName);
                            }
                            else
                            {
                                result.Errors.Add($"Failed to update metadata for {s3Object.Key}");
                            }
                        }
                        else
                        {
                            result.ObjectsUpdated++; // Would be updated
                            _logger.LogInformation("DRY RUN: Would update {Key} with metadata for {CatName}", s3Object.Key, catName);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing S3 object: {Key}", s3Object.Key);
                        result.Errors.Add($"Error processing {s3Object.Key}: {ex.Message}");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing category: {Category}", category);
                result.Errors.Add($"Category {category}: {ex.Message}");
                return result;
            }
        }

        private string? ExtractCatNameFromS3Key(string s3Key)
        {
            // Handle different S3 key patterns:
            // queens/Ghostgums-4-030324-1.png -> Ghostgums
            // queens/Indy/Indy-8-010123-1.png -> Indy  
            // queens/Cat1/TESTING-IMG_4275.jpg -> Cat1

            var relativePath = _s3StorageService.RemoveKeyPrefix(s3Key);
            var parts = relativePath.Split('/');

            if (parts.Length == 2)
            {
                // Direct in category: queens/Ghostgums-4-030324-1.png
                var fileName = Path.GetFileNameWithoutExtension(parts[1]);
                // Extract cat name (everything before first dash/number)
                var catName = ExtractCatNameFromFileName(fileName);
                return catName;
            }
            else if (parts.Length == 3)
            {
                // In subfolder: queens/Indy/Indy-8-010123-1.png
                return parts[1]; // Use folder name as cat name
            }

            return null;
        }

        private string ExtractCatNameFromFileName(string fileName)
        {
            // Remove common prefixes
            fileName = fileName.Replace("TESTING-", "")
                              .Replace("IMG_", "")
                              .Replace("DSC_", "");

            // Split on common delimiters and take first meaningful part
            var parts = fileName.Split(new[] { '-', '_', ' ' }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (var part in parts)
            {
                // Skip pure numbers or very short parts
                if (part.All(char.IsDigit) || part.Length < 3)
                    continue;
                    
                return char.ToUpper(part[0]) + part.Substring(1);
            }

            return fileName; // Fallback to full filename
        }

        private bool HasRichMetadata(Dictionary<string, string>? metadata)
        {
            if (metadata == null || metadata.Count == 0)
                return false;

            // Check for key indicators of rich metadata
            var richKeys = new[] { "cat-name", "description", "personality", "bloodline" };
            return richKeys.Any(key => 
                metadata.ContainsKey(key) && !string.IsNullOrWhiteSpace(metadata[key]));
        }

        private Dictionary<string, string> PrepareS3Metadata(string catName, CatDescriptionEntry description, string category)
        {
            var metadata = new Dictionary<string, string>
            {
                ["cat-name"] = catName,
                ["description"] = description.Description ?? "",
                ["breed"] = "Maine Coon", // Default for your cattery
                ["category"] = category
            };

            // Extract additional info from description text
            var desc = description.Description ?? "";
            
            // Try to extract gender
            if (category == "studs" || desc.ToLower().Contains(" boy ") || desc.ToLower().Contains(" he "))
                metadata["gender"] = "M";
            else if (category == "queens" || desc.ToLower().Contains(" girl ") || desc.ToLower().Contains(" she "))
                metadata["gender"] = "F";

            // Try to extract color/pattern from description
            var colors = new[] { "black", "blue", "red", "silver", "smoke", "tabby", "tortie", "white" };
            var foundColors = colors.Where(color => desc.ToLower().Contains(color)).ToList();
            if (foundColors.Any())
            {
                metadata["color"] = string.Join(" ", foundColors);
            }

            // Try to extract age/dates from description
            var ageMatches = System.Text.RegularExpressions.Regex.Matches(desc, @"(\d+)\s+(years?|months?)\s+old");
            if (ageMatches.Count > 0)
            {
                metadata["age"] = ageMatches[0].Groups[0].Value;
            }

            // Extract bloodline/lineage info
            if (desc.Contains("daughter") || desc.Contains("son") || desc.Contains("granddaughter") || desc.Contains("grandson"))
            {
                metadata["bloodline"] = "Champion lineage"; // You could make this more sophisticated
            }

            // Mark source for tracking
            metadata["metadata-source"] = "cat-descriptions.json";
            metadata["migrated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

            return metadata;
        }

        private async Task<bool> WriteMetadataToS3Async(string s3Key, Dictionary<string, string> metadata)
        {
            try
            {
                // Use the existing S3 service to write metadata
                // This might need to be extended if it doesn't support metadata updates
                await _s3StorageService.UpdateObjectMetadataAsync(s3Key, metadata);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to write metadata to S3 object: {Key}", s3Key);
                return false;
            }
        }

        private bool IsImageFile(string key)
        {
            var extension = Path.GetExtension(key).ToLowerInvariant();
            return new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp" }.Contains(extension);
        }
    }

    public class CatDescriptionEntry
    {
        public string Description { get; set; } = string.Empty;
    }

    public class MigrationResult
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public bool IsDryRun { get; set; }
        public string? Category { get; set; }
        public string? ErrorMessage { get; set; }
        
        public int CategoriesProcessed { get; set; }
        public int ObjectsProcessed { get; set; }
        public int ObjectsUpdated { get; set; }
        public int ObjectsSkipped { get; set; }
        
        public List<string> Errors { get; set; } = new();
        public Dictionary<string, CategoryMigrationResult> CategoryResults { get; set; } = new();
    }

    public class CategoryMigrationResult
    {
        public string Category { get; set; } = string.Empty;
        public int ObjectsProcessed { get; set; }
        public int ObjectsUpdated { get; set; }
        public int ObjectsSkipped { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}
