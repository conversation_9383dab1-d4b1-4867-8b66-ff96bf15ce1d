using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Public gallery controller for viewing cat photos (no authentication required)
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PublicGalleryController : ControllerBase
    {
        private readonly ILogger<PublicGalleryController> _logger;
        private readonly IPhotoIndexService _photoIndexService;
        private readonly ISmartImageService _smartImageService;
        private readonly AppDbContext _context;
        private readonly IS3StorageService _s3StorageService;

        public PublicGalleryController(
            ILogger<PublicGalleryController> logger,
            IPhotoIndexService photoIndexService,
            ISmartImageService smartImageService,
            AppDbContext context,
            IS3StorageService s3StorageService)
        {
            _logger = logger;
            _photoIndexService = photoIndexService;
            _smartImageService = smartImageService;
            _context = context;
            _s3StorageService = s3StorageService;
        }

        /// <summary>
        /// Get all cat photos in a category (public access)
        /// </summary>
        /// <param name="category">Category name (studs, queens, kittens, gallery)</param>
        /// <param name="sortBy">Sort by: name, date, random</param>
        /// <param name="includeMetadata">Include detailed metadata</param>
        /// <returns>List of cat photos with public URLs</returns>
        [HttpGet("category/{category}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetCategoryImages(
            string category, 
            string sortBy = "name", 
            bool includeMetadata = false)
        {
            try
            {
                _logger.LogInformation("Public request for category: {Category}", category);

                // Validate category
                var validCategories = new[] { "studs", "queens", "kittens", "gallery" };
                if (!validCategories.Contains(category.ToLower()))
                {
                    return BadRequest(new { message = "Invalid category. Valid categories: studs, queens, kittens, gallery" });
                }

                // Try database first (fastest)
                var dbImages = await _context.CatGalleryImages
                    .Where(img => img.Category.ToLower() == category.ToLower())
                    .ToListAsync();

                if (dbImages.Any())
                {
                    _logger.LogInformation("Found {Count} images in database for category {Category}", dbImages.Count, category);

                    var sortedImages = SortImages(dbImages, sortBy);
                    var publicImages = new List<object>();
                    foreach (var img in sortedImages)
                    {
                        publicImages.Add(await CreatePublicImageResponseAsync(img, includeMetadata));
                    }

                    return Ok(new
                    {
                        category = category,
                        count = publicImages.Count,
                        sortBy = sortBy,
                        images = publicImages,
                        source = "database",
                        retrievedAt = DateTime.UtcNow
                    });
                }

                // Fallback to photo index (no S3 credentials required)
                _logger.LogInformation("No database records found, using photo index for category: {Category}", category);

                var indexImages = await _photoIndexService.GetCategoryPhotosAsync(category);
                var sortedIndexImages = SortImages(indexImages, sortBy);
                var publicIndexImages = new List<object>();
                foreach (var img in sortedIndexImages)
                {
                    publicIndexImages.Add(await CreatePublicImageResponseAsync(img, includeMetadata));
                }

                return Ok(new
                {
                    category = category,
                    count = publicIndexImages.Count,
                    sortBy = sortBy,
                    images = publicIndexImages,
                    source = "index",
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving public gallery for category: {Category}", category);
                return StatusCode(500, new { message = "An error occurred while retrieving the gallery" });
            }
        }

        /// <summary>
        /// Get photos for a specific cat (public access)
        /// </summary>
        /// <param name="catName">Name of the cat</param>
        /// <param name="sortBy">Sort by: date, name, random</param>
        /// <param name="includeMetadata">Include detailed metadata</param>
        /// <returns>List of photos for the specified cat</returns>
        [HttpGet("cat/{catName}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetCatPhotos(
            string catName, 
            string sortBy = "date", 
            bool includeMetadata = false)
        {
            try
            {
                _logger.LogInformation("Public request for cat photos: {CatName}", catName);

                // Try database first
                var dbImages = await _context.CatGalleryImages
                    .Where(img => img.CatName.ToLower() == catName.ToLower())
                    .ToListAsync();

                if (dbImages.Any())
                {
                    var sortedImages = SortImages(dbImages, sortBy);
                    var publicImages = new List<object>();
                    foreach (var img in sortedImages)
                    {
                        publicImages.Add(await CreatePublicImageResponseAsync(img, includeMetadata));
                    }

                    return Ok(new
                    {
                        catName = catName,
                        count = publicImages.Count,
                        sortBy = sortBy,
                        images = publicImages,
                        source = "database",
                        retrievedAt = DateTime.UtcNow
                    });
                }

                // Fallback to photo index (no S3 credentials required)
                var indexImages = await _photoIndexService.GetCatPhotosAsync(catName);

                if (!indexImages.Any())
                {
                    return NotFound(new { message = $"No photos found for cat: {catName}" });
                }

                var sortedIndexImages = SortImages(indexImages, sortBy);
                var publicIndexImages = new List<object>();
                foreach (var img in sortedIndexImages)
                {
                    publicIndexImages.Add(await CreatePublicImageResponseAsync(img, includeMetadata));
                }

                return Ok(new
                {
                    catName = catName,
                    count = publicIndexImages.Count,
                    sortBy = sortBy,
                    images = publicIndexImages,
                    source = "index",
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving photos for cat: {CatName}", catName);
                return StatusCode(500, new { message = "An error occurred while retrieving cat photos" });
            }
        }

        /// <summary>
        /// Get random featured photos (public access)
        /// </summary>
        /// <param name="count">Number of photos to return (max 20)</param>
        /// <returns>Random selection of cat photos</returns>
        [HttpGet("featured")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetFeaturedPhotos(int count = 6)
        {
            try
            {
                count = Math.Min(count, 20); // Limit to 20 photos max

                var allImages = await _context.CatGalleryImages
                    .Where(img => !string.IsNullOrEmpty(img.CatName))
                    .ToListAsync();

                if (!allImages.Any())
                {
                    // Fallback to photo index if no database records
                    allImages = await _photoIndexService.GetFeaturedPhotosAsync(count * 2); // Get more to have selection
                }

                var random = new Random();
                var featuredImagesList = allImages
                    .OrderBy(x => random.Next())
                    .Take(count)
                    .ToList();

                var featuredImages = new List<object>();
                foreach (var img in featuredImagesList)
                {
                    featuredImages.Add(await CreatePublicImageResponseAsync(img, true));
                }

                return Ok(new
                {
                    count = featuredImages.Count,
                    images = featuredImages,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured photos");
                return StatusCode(500, new { message = "An error occurred while retrieving featured photos" });
            }
        }

        private async Task<object> CreatePublicImageResponseAsync(CatGalleryImage image, bool includeMetadata)
        {
            // PRIORITY SYSTEM: Check S3 metadata first, then fallback to database values
            string catName = image.CatName ?? "Unknown";
            string description = image.Description;
            string breed = image.Breed;
            string gender = image.Gender;
            string ageAtPhoto = image.AgeAtPhoto;

            // Always try to get S3 metadata if we have a storage key - S3 metadata takes priority
            if (!string.IsNullOrEmpty(image.StorageKey))
            {
                try
                {
                    var s3Metadata = await _s3StorageService.GetObjectMetadataDirectAsync(image.StorageKey);
                    if (s3Metadata != null && s3Metadata.Count > 0)
                    {
                        // Extract metadata with priority over database values
                        var s3CatName = GetMetadataValue(s3Metadata, "cat-name", "name", "catname");
                        if (!string.IsNullOrEmpty(s3CatName))
                        {
                            catName = s3CatName;
                        }

                        var s3Description = GetMetadataValue(s3Metadata, "description", "desc");
                        if (!string.IsNullOrEmpty(s3Description))
                        {
                            description = s3Description;
                        }

                        var s3Breed = GetMetadataValue(s3Metadata, "breed", "cat-breed");
                        if (!string.IsNullOrEmpty(s3Breed))
                        {
                            breed = s3Breed;
                        }

                        var s3Gender = GetMetadataValue(s3Metadata, "gender", "sex");
                        if (!string.IsNullOrEmpty(s3Gender))
                        {
                            gender = s3Gender;
                        }

                        var s3Age = GetMetadataValue(s3Metadata, "age", "age-at-photo", "cat-age");
                        if (!string.IsNullOrEmpty(s3Age))
                        {
                            ageAtPhoto = s3Age;
                        }

                        // Extract date taken from S3 metadata
                        var s3DateTaken = GetMetadataValue(s3Metadata, "date-taken", "photo-date", "taken-date");
                        if (!string.IsNullOrEmpty(s3DateTaken) && DateTime.TryParse(s3DateTaken, out var parsedDateTaken))
                        {
                            image.DateTaken = parsedDateTaken;
                        }

                        _logger.LogInformation("Using S3 metadata for {StorageKey}: CatName='{CatName}', Breed='{Breed}', Gender='{Gender}', Age='{Age}', DateTaken='{DateTaken}'",
                            image.StorageKey, catName, breed, gender, ageAtPhoto, s3DateTaken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to retrieve S3 metadata for {StorageKey}, using database values", image.StorageKey);
                }
            }

            var basicResponse = new
            {
                id = image.Id,
                catName = catName,
                imageUrl = image.ImageUrl, // This uses the public URL template
                category = image.Category,
                dateTaken = image.DateTaken,
                orderNumber = image.OrderNumber
            };

            if (!includeMetadata)
            {
                return basicResponse;
            }

            return new
            {
                id = image.Id,
                catName = catName,
                imageUrl = image.ImageUrl,
                category = image.Category,
                dateTaken = image.DateTaken,
                orderNumber = image.OrderNumber,
                age = image.Age,
                description = description,
                color = image.Color,
                gender = gender,
                traits = image.Traits,
                breed = breed,
                personality = image.Personality,
                fileFormat = image.FileFormat,
                fileSize = image.FileSize,
                width = image.Width,
                height = image.Height
            };
        }

        private List<CatGalleryImage> SortImages(List<CatGalleryImage> images, string sortBy)
        {
            return sortBy.ToLower() switch
            {
                "date" => images.OrderByDescending(img => img.DateTaken).ToList(),
                "name" => images.OrderBy(img => img.CatName).ThenBy(img => img.DateTaken).ToList(),
                "random" => images.OrderBy(x => Guid.NewGuid()).ToList(),
                _ => images.OrderBy(img => img.CatName).ThenBy(img => img.DateTaken).ToList()
            };
        }

        /// <summary>
        /// Check if image data looks like fallback data that should be overwritten with S3 metadata
        /// </summary>
        private bool IsLikelyFallbackData(CatGalleryImage image)
        {
            // Check for fallback cat names
            if (!string.IsNullOrEmpty(image.CatName))
            {
                var lowerName = image.CatName.ToLower();
                if (lowerName.Contains("beautiful queen") ||
                    lowerName.Contains("beautiful stud") ||
                    lowerName.Contains("beautiful kitten") ||
                    lowerName.StartsWith("img_") ||
                    lowerName.StartsWith("dsc_") ||
                    lowerName == "yendorcats")
                {
                    return true;
                }
            }

            // Check if breed is just the default and other metadata is missing
            if (image.Breed == "Maine Coon" && string.IsNullOrEmpty(image.Description) &&
                string.IsNullOrEmpty(image.Tags) && string.IsNullOrEmpty(image.Gender))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Get metadata value by checking multiple possible keys (case-insensitive)
        /// </summary>
        private string GetMetadataValue(Dictionary<string, string> metadata, params string[] possibleKeys)
        {
            foreach (var key in possibleKeys)
            {
                // Check exact match first
                if (metadata.TryGetValue(key, out var value) && !string.IsNullOrWhiteSpace(value))
                    return value.Trim();

                // Check case-insensitive match
                var kvp = metadata.FirstOrDefault(m => string.Equals(m.Key, key, StringComparison.OrdinalIgnoreCase));
                if (!string.IsNullOrEmpty(kvp.Key) && !string.IsNullOrWhiteSpace(kvp.Value))
                    return kvp.Value.Trim();
            }
            return null;
        }

        /// <summary>
        /// Get all cat photos in a category using smart loading with B2-first priority chain
        /// </summary>
        /// <param name="category">Category name (studs, queens, kittens, gallery)</param>
        /// <param name="sortBy">Sort by: name, date, random</param>
        /// <param name="includeMetadata">Include detailed metadata</param>
        /// <returns>List of cat photos with smart URLs and fallback chain</returns>
        [HttpGet("smart/{category}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetSmartCategoryImages(
            string category, 
            string sortBy = "date", 
            bool includeMetadata = false)
        {
            try
            {
                _logger.LogInformation("Smart loading request for category: {Category}", category);

                // Validate category
                var validCategories = new[] { "studs", "queens", "kittens", "gallery" };
                if (!validCategories.Contains(category.ToLower()))
                {
                    return BadRequest(new { message = "Invalid category. Valid categories: studs, queens, kittens, gallery" });
                }

                var smartResponse = await _smartImageService.GetCategoryImagesAsync(category, sortBy, includeMetadata);

                return Ok(smartResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in smart loading for category: {Category}", category);
                return StatusCode(500, new { message = "An error occurred while retrieving the smart gallery" });
            }
        }

        /// <summary>
        /// Get photos for a specific cat using smart loading
        /// </summary>
        /// <param name="catName">Name of the cat</param>
        /// <param name="sortBy">Sort by: date, name, random</param>
        /// <param name="includeMetadata">Include detailed metadata</param>
        /// <returns>List of photos for the specified cat with smart URLs</returns>
        [HttpGet("smart/cat/{catName}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetSmartCatPhotos(
            string catName, 
            string sortBy = "date", 
            bool includeMetadata = false)
        {
            try
            {
                _logger.LogInformation("Smart loading request for cat photos: {CatName}", catName);

                var smartResponse = await _smartImageService.GetCatImagesAsync(catName, sortBy, includeMetadata);

                if (smartResponse.Count == 0)
                {
                    return NotFound(new { message = $"No photos found for cat: {catName}" });
                }

                return Ok(smartResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in smart loading for cat: {CatName}", catName);
                return StatusCode(500, new { message = "An error occurred while retrieving cat photos" });
            }
        }

        /// <summary>
        /// Check if an image is available at the B2 URL
        /// </summary>
        /// <param name="url">B2 URL to check</param>
        /// <returns>Availability status</returns>
        [HttpGet("check-availability")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> CheckImageAvailability([FromQuery] string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url))
                {
                    return BadRequest(new { message = "URL parameter is required" });
                }

                var isAvailable = await _smartImageService.CheckImageAvailabilityAsync(url);

                return Ok(new 
                { 
                    url = url,
                    isAvailable = isAvailable,
                    checkedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking image availability for URL: {Url}", url);
                return StatusCode(500, new { message = "An error occurred while checking image availability" });
            }
        }

        /// <summary>
        /// Get a single image with full fallback chain
        /// </summary>
        /// <param name="category">Category name</param>
        /// <param name="catName">Cat name</param>
        /// <param name="filename">Image filename</param>
        /// <returns>Single image with fallback URLs</returns>
        [HttpGet("smart/{category}/{catName}/{filename}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetImageWithFallback(string category, string catName, string filename)
        {
            try
            {
                _logger.LogInformation("Smart image request: {Category}/{CatName}/{Filename}", category, catName, filename);

                var smartImage = await _smartImageService.GetImageWithFallbackAsync(category, catName, filename);

                return Ok(smartImage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting image with fallback: {Category}/{CatName}/{Filename}", category, catName, filename);
                return StatusCode(500, new { message = "An error occurred while retrieving the image" });
            }
        }

        private bool IsImageFile(string fileName)
        {
            var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp" };
            var extension = Path.GetExtension(fileName).ToLower();
            return imageExtensions.Contains(extension);
        }


    }
}
