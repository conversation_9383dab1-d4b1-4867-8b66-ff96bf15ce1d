using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Attributes;
using YendorCats.API.Services;
using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for managing S3 object metadata
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize("SuperAdmin", "Admin", "Editor")]
    public class S3MetadataController : ControllerBase
    {
        private readonly IS3StorageService _s3StorageService;
        private readonly ILogger<S3MetadataController> _logger;

        public S3MetadataController(
            IS3StorageService s3StorageService,
            ILogger<S3MetadataController> logger)
        {
            _s3StorageService = s3StorageService;
            _logger = logger;
        }

        /// <summary>
        /// Update metadata for an S3 object
        /// </summary>
        /// <param name="request">Metadata update request</param>
        /// <returns>Success response</returns>
        [HttpPost("update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateMetadata([FromBody] S3MetadataUpdateRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var validationErrors = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .SelectMany(x => x.Value.Errors.Select(e => $"{x.Key}: {e.ErrorMessage}"))
                        .ToList();

                    _logger.LogWarning("Validation failed for S3 metadata update: {ValidationErrors}",
                        string.Join(", ", validationErrors));

                    return BadRequest(new {
                        success = false,
                        message = "Validation failed",
                        errors = validationErrors
                    });
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} updating metadata for S3 key: {S3Key}",
                    adminUser?.Username, request.S3Key);

                // Validate S3 key format and sanitize
                if (string.IsNullOrWhiteSpace(request.S3Key))
                {
                    return BadRequest(new {
                        success = false,
                        message = "S3 key cannot be empty"
                    });
                }

                // Create CatImageMetadata object from request
                var catImageMetadata = new Models.CatImageMetadata
                {
                    Name = request.CatName ?? string.Empty,
                    Age = request.Age,
                    DateTaken = !string.IsNullOrEmpty(request.DateTaken) && DateTime.TryParse(request.DateTaken, out var dateTaken) ? dateTaken : null,
                    Description = request.Description,
                    Breed = request.Breed,
                    Gender = request.Gender ?? string.Empty,
                    HairColor = request.Color,
                    Personality = request.Personality,
                    Bloodline = request.Bloodline,
                    CatId = !string.IsNullOrEmpty(request.CatId) && int.TryParse(request.CatId, out var catId) ? catId : null,
                    RegisteredName = request.RegisteredName,
                    RegistrationNumber = request.RegistrationNumber,
                    FatherCatId = !string.IsNullOrEmpty(request.FatherId) && int.TryParse(request.FatherId, out var fatherId) ? fatherId : null,
                    MotherCatId = !string.IsNullOrEmpty(request.MotherId) && int.TryParse(request.MotherId, out var motherId) ? motherId : null,
                    BreedingStatus = request.BreedingStatus,
                    AvailabilityStatus = request.AvailabilityStatus,
                    PhotoType = request.PhotoType,
                    AgeAtPhoto = request.AgeAtPhoto,
                    Tags = request.Tags,
                    ChampionTitles = request.ChampionTitles,
                    GenerationLevel = request.GenerationLevel,
                    DateUploaded = DateTime.UtcNow,
                    FileFormat = "jpg", // Default, should be determined from S3 object
                    ContentType = "image/jpeg" // Default, should be determined from S3 object
                };

                // Convert to S3 metadata format using the model's method
                var metadata = catImageMetadata.ToS3Metadata();
                // Add system metadata
                metadata["updated-by"] = adminUser?.Username ?? "system";
                metadata["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

                // Update S3 object metadata by copying to itself with new metadata
                await UpdateS3ObjectMetadata(request.S3Key, metadata);

                _logger.LogInformation("Successfully updated metadata for S3 key: {S3Key}", request.S3Key);

                return Ok(new {
                    success = true,
                    message = "Metadata updated successfully",
                    s3Key = request.S3Key,
                    updatedBy = adminUser?.Username,
                    updatedAt = DateTime.UtcNow
                });
            }
            catch (Amazon.S3.AmazonS3Exception s3Ex)
            {
                _logger.LogError(s3Ex, "S3 error updating metadata for S3 key: {S3Key}", request.S3Key);
                
                return s3Ex.StatusCode switch
                {
                    System.Net.HttpStatusCode.NotFound => NotFound(new {
                        success = false,
                        message = $"S3 object not found: {request.S3Key}",
                        errorCode = "OBJECT_NOT_FOUND"
                    }),
                    System.Net.HttpStatusCode.Forbidden => StatusCode(403, new {
                        success = false,
                        message = "Access denied to S3 object",
                        errorCode = "ACCESS_DENIED"
                    }),
                    System.Net.HttpStatusCode.BadRequest => BadRequest(new {
                        success = false,
                        message = "Invalid S3 request",
                        errorCode = "INVALID_REQUEST"
                    }),
                    _ => StatusCode(500, new {
                        success = false,
                        message = $"S3 service error: {s3Ex.Message}",
                        errorCode = "S3_SERVICE_ERROR"
                    })
                };
            }
            catch (ArgumentException argEx)
            {
                _logger.LogError(argEx, "Invalid argument for S3 metadata update: {S3Key}", request.S3Key);
                return BadRequest(new {
                    success = false,
                    message = $"Invalid input: {argEx.Message}",
                    errorCode = "INVALID_ARGUMENT"
                });
            }
            catch (TimeoutException timeoutEx)
            {
                _logger.LogError(timeoutEx, "Timeout updating metadata for S3 key: {S3Key}", request.S3Key);
                return StatusCode(408, new {
                    success = false,
                    message = "Request timeout while updating metadata",
                    errorCode = "TIMEOUT"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error updating metadata for S3 key: {S3Key}", request.S3Key);
                return StatusCode(500, new {
                    success = false,
                    message = "An unexpected error occurred while updating metadata",
                    errorCode = "INTERNAL_ERROR",
                    details = ex.Message
                });
            }
        }

        /// <summary>
        /// Get current metadata for an S3 object
        /// </summary>
        /// <param name="s3Key">S3 object key</param>
        /// <returns>Current metadata</returns>
        [HttpGet("get/{*s3Key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetMetadata(string s3Key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(s3Key))
                {
                    return BadRequest(new {
                        success = false,
                        message = "S3 key cannot be empty",
                        errorCode = "INVALID_S3_KEY"
                    });
                }

                _logger.LogInformation("Retrieving metadata for S3 key: {S3Key}", s3Key);

                var metadata = await _s3StorageService.GetObjectMetadataDirectAsync(s3Key);

                return Ok(new {
                    success = true,
                    s3Key = s3Key,
                    metadata = metadata,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Amazon.S3.AmazonS3Exception s3Ex)
            {
                _logger.LogError(s3Ex, "S3 error retrieving metadata for S3 key: {S3Key}", s3Key);
                
                return s3Ex.StatusCode switch
                {
                    System.Net.HttpStatusCode.NotFound => NotFound(new {
                        success = false,
                        message = $"S3 object not found: {s3Key}",
                        errorCode = "OBJECT_NOT_FOUND"
                    }),
                    System.Net.HttpStatusCode.Forbidden => StatusCode(403, new {
                        success = false,
                        message = "Access denied to S3 object",
                        errorCode = "ACCESS_DENIED"
                    }),
                    _ => StatusCode(500, new {
                        success = false,
                        message = $"S3 service error: {s3Ex.Message}",
                        errorCode = "S3_SERVICE_ERROR"
                    })
                };
            }
            catch (ArgumentException argEx)
            {
                _logger.LogError(argEx, "Invalid argument for S3 metadata retrieval: {S3Key}", s3Key);
                return BadRequest(new {
                    success = false,
                    message = $"Invalid S3 key format: {argEx.Message}",
                    errorCode = "INVALID_ARGUMENT"
                });
            }
            catch (TimeoutException timeoutEx)
            {
                _logger.LogError(timeoutEx, "Timeout retrieving metadata for S3 key: {S3Key}", s3Key);
                return StatusCode(408, new {
                    success = false,
                    message = "Request timeout while retrieving metadata",
                    errorCode = "TIMEOUT"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error retrieving metadata for S3 key: {S3Key}", s3Key);
                return StatusCode(500, new {
                    success = false,
                    message = "An unexpected error occurred while retrieving metadata",
                    errorCode = "INTERNAL_ERROR",
                    details = ex.Message
                });
            }
        }

        /// <summary>
        /// Bulk update metadata for multiple S3 objects
        /// </summary>
        /// <param name="requests">List of metadata update requests</param>
        /// <returns>Bulk update results</returns>
        [HttpPost("bulk-update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> BulkUpdateMetadata([FromBody] List<S3MetadataUpdateRequest> requests)
        {
            var transactionId = Guid.NewGuid().ToString("N")[..8];
            var successfulUpdates = new List<string>(); // Track successful updates for potential rollback
            
            try
            {
                if (!ModelState.IsValid)
                {
                    var validationErrors = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .SelectMany(x => x.Value.Errors.Select(e => $"{x.Key}: {e.ErrorMessage}"))
                        .ToList();

                    _logger.LogWarning("Validation failed for bulk S3 metadata update: {ValidationErrors}",
                        string.Join(", ", validationErrors));

                    return BadRequest(new {
                        success = false,
                        message = "Validation failed",
                        errors = validationErrors
                    });
                }

                if (requests == null || requests.Count == 0)
                {
                    return BadRequest(new {
                        success = false,
                        message = "No requests provided for bulk update",
                        errorCode = "EMPTY_REQUEST_LIST"
                    });
                }

                if (requests.Count > 100) // Limit bulk operations to prevent resource exhaustion
                {
                    return BadRequest(new {
                        success = false,
                        message = "Bulk update limited to 100 items per request",
                        errorCode = "BULK_LIMIT_EXCEEDED"
                    });
                }

                var adminUser = HttpContext.GetAdminUser();
                var results = new List<object>();
                var successCount = 0;
                var failedCount = 0;
                var checkpointCount = 0;

                _logger.LogInformation("Admin {Username} performing bulk metadata update for {Count} objects (Transaction: {TransactionId})",
                    adminUser?.Username, requests.Count, transactionId);

                // Process requests in batches for better error recovery
                const int batchSize = 10;
                var batches = requests.Select((request, index) => new { request, index })
                    .GroupBy(x => x.index / batchSize)
                    .Select(g => g.Select(x => x.request).ToList())
                    .ToList();

                foreach (var batch in batches)
                {
                    var batchResults = new List<object>();
                    var batchSuccessCount = 0;

                    foreach (var request in batch)
                    {
                        var result = await ProcessSingleUpdateWithRetry(request, adminUser, transactionId);
                        batchResults.Add(result);
                        
                        if (result.success)
                        {
                            successfulUpdates.Add(request.S3Key);
                            batchSuccessCount++;
                            successCount++;
                        }
                        else
                        {
                            failedCount++;
                        }

                        // Small delay to avoid rate limiting
                        await Task.Delay(100);
                    }

                    results.AddRange(batchResults);
                    checkpointCount++;

                    _logger.LogInformation("Batch {BatchNumber} completed: {SuccessCount}/{BatchSize} successful (Transaction: {TransactionId})",
                        checkpointCount, batchSuccessCount, batch.Count, transactionId);

                    // If batch fails significantly, offer early termination option
                    if (batchSuccessCount == 0 && batch.Count > 1)
                    {
                        _logger.LogWarning("Entire batch failed, continuing with next batch (Transaction: {TransactionId})",
                            transactionId);
                    }
                }

                var responseMessage = $"Bulk update completed: {successCount}/{requests.Count} successful";
                if (failedCount > 0)
                {
                    responseMessage += $", {failedCount} failed";
                }

                _logger.LogInformation("Bulk update transaction {TransactionId} completed: {SuccessCount}/{Total} successful",
                    transactionId, successCount, requests.Count);

                return Ok(new {
                    success = true,
                    message = responseMessage,
                    transactionId = transactionId,
                    summary = new {
                        total = requests.Count,
                        successful = successCount,
                        failed = failedCount,
                        successRate = Math.Round((double)successCount / requests.Count * 100, 2),
                        batchesProcessed = checkpointCount,
                        canRollback = successfulUpdates.Count > 0
                    },
                    results = results,
                    updatedBy = adminUser?.Username,
                    completedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during bulk metadata update (Transaction: {TransactionId}). Successful updates: {SuccessfulCount}",
                    transactionId, successfulUpdates.Count);
                
                // In case of catastrophic failure, log successful updates for manual intervention
                if (successfulUpdates.Count > 0)
                {
                    _logger.LogWarning("Transaction {TransactionId} failed with {SuccessfulCount} successful updates. S3 keys: {S3Keys}",
                        transactionId, successfulUpdates.Count, string.Join(", ", successfulUpdates));
                }

                return StatusCode(500, new {
                    success = false,
                    message = "An unexpected error occurred during bulk update",
                    errorCode = "BULK_UPDATE_ERROR",
                    transactionId = transactionId,
                    partialSuccess = successfulUpdates.Count > 0,
                    successfulUpdates = successfulUpdates.Count,
                    details = ex.Message
                });
            }
        }

        private async Task<dynamic> ProcessSingleUpdateWithRetry(S3MetadataUpdateRequest request, dynamic adminUser, string transactionId)
        {
            const int maxRetries = 3;
            const int baseDelayMs = 1000;
            
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    // Validate individual request
                    if (string.IsNullOrWhiteSpace(request.S3Key))
                    {
                        return new {
                            s3Key = request.S3Key ?? "null",
                            success = false,
                            message = "S3 key cannot be empty",
                            errorCode = "INVALID_S3_KEY",
                            attempt = attempt
                        };
                    }

                    // Create CatImageMetadata object from request
                    var catImageMetadata = new Models.CatImageMetadata
                    {
                        Name = request.CatName ?? string.Empty,
                        Age = request.Age,
                        DateTaken = !string.IsNullOrEmpty(request.DateTaken) && DateTime.TryParse(request.DateTaken, out var dateTaken) ? dateTaken : null,
                        Description = request.Description,
                        Breed = request.Breed,
                        Gender = request.Gender ?? string.Empty,
                        HairColor = request.Color,
                        Personality = request.Personality,
                        Bloodline = request.Bloodline,
                        CatId = !string.IsNullOrEmpty(request.CatId) && int.TryParse(request.CatId, out var catId) ? catId : null,
                        RegisteredName = request.RegisteredName,
                        RegistrationNumber = request.RegistrationNumber,
                        FatherCatId = !string.IsNullOrEmpty(request.FatherId) && int.TryParse(request.FatherId, out var fatherId) ? fatherId : null,
                        MotherCatId = !string.IsNullOrEmpty(request.MotherId) && int.TryParse(request.MotherId, out var motherId) ? motherId : null,
                        BreedingStatus = request.BreedingStatus,
                        AvailabilityStatus = request.AvailabilityStatus,
                        PhotoType = request.PhotoType,
                        AgeAtPhoto = request.AgeAtPhoto,
                        Tags = request.Tags,
                        ChampionTitles = request.ChampionTitles,
                        GenerationLevel = request.GenerationLevel,
                        DateUploaded = DateTime.UtcNow,
                        FileFormat = "jpg", // Default, should be determined from S3 object
                        ContentType = "image/jpeg" // Default, should be determined from S3 object
                    };

                    // Convert to S3 metadata format using the model's method
                    var metadata = catImageMetadata.ToS3Metadata();
                    
                    // Add system metadata
                    metadata["updated-by"] = adminUser?.Username ?? "system";
                    metadata["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");
                    metadata["transaction-id"] = transactionId;

                    await UpdateS3ObjectMetadata(request.S3Key, metadata);

                    return new {
                        s3Key = request.S3Key,
                        success = true,
                        message = attempt > 1 ? $"Updated successfully (attempt {attempt})" : "Updated successfully",
                        attempt = attempt
                    };
                }
                catch (Amazon.S3.AmazonS3Exception s3Ex) when (IsRetryableS3Exception(s3Ex) && attempt < maxRetries)
                {
                    _logger.LogWarning(s3Ex, "Retryable S3 error on attempt {Attempt} for S3 key: {S3Key} (Transaction: {TransactionId})",
                        attempt, request.S3Key, transactionId);
                    
                    var delayMs = baseDelayMs * (int)Math.Pow(2, attempt - 1); // Exponential backoff
                    await Task.Delay(delayMs);
                    continue;
                }
                catch (Amazon.S3.AmazonS3Exception s3Ex)
                {
                    _logger.LogError(s3Ex, "S3 error updating metadata for S3 key: {S3Key} (Transaction: {TransactionId})",
                        request.S3Key, transactionId);
                    
                    var errorMessage = s3Ex.StatusCode switch
                    {
                        System.Net.HttpStatusCode.NotFound => "Object not found",
                        System.Net.HttpStatusCode.Forbidden => "Access denied",
                        System.Net.HttpStatusCode.BadRequest => "Invalid request",
                        _ => $"S3 service error: {s3Ex.Message}"
                    };

                    return new {
                        s3Key = request.S3Key,
                        success = false,
                        message = errorMessage,
                        errorCode = s3Ex.ErrorCode,
                        attempt = attempt
                    };
                }
                catch (Exception ex) when (IsRetryableException(ex) && attempt < maxRetries)
                {
                    _logger.LogWarning(ex, "Retryable error on attempt {Attempt} for S3 key: {S3Key} (Transaction: {TransactionId})",
                        attempt, request.S3Key, transactionId);
                    
                    var delayMs = baseDelayMs * (int)Math.Pow(2, attempt - 1); // Exponential backoff
                    await Task.Delay(delayMs);
                    continue;
                }
                catch (Exception ex) when (IsCatastrophicException(ex))
                {
                    _logger.LogError(ex, "Catastrophic error updating metadata for S3 key: {S3Key} (Transaction: {TransactionId})",
                        request.S3Key, transactionId);
                    
                    // Re-throw catastrophic exceptions to fail the entire bulk operation
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating metadata for S3 key: {S3Key} (Transaction: {TransactionId})",
                        request.S3Key, transactionId);
                    
                    return new {
                        s3Key = request.S3Key,
                        success = false,
                        message = ex.Message,
                        errorCode = "INTERNAL_ERROR",
                        attempt = attempt
                    };
                }
            }

            // This should never be reached due to the loop structure, but included for completeness
            return new {
                s3Key = request.S3Key,
                success = false,
                message = "Maximum retry attempts exceeded",
                errorCode = "MAX_RETRIES_EXCEEDED",
                attempt = maxRetries
            };
        }

        private static bool IsRetryableS3Exception(Amazon.S3.AmazonS3Exception ex)
        {
            return ex.StatusCode switch
            {
                System.Net.HttpStatusCode.InternalServerError => true,
                System.Net.HttpStatusCode.BadGateway => true,
                System.Net.HttpStatusCode.ServiceUnavailable => true,
                System.Net.HttpStatusCode.GatewayTimeout => true,
                System.Net.HttpStatusCode.TooManyRequests => true,
                _ => false
            };
        }

        private static bool IsRetryableException(Exception ex)
        {
            return ex is TimeoutException ||
                   ex is TaskCanceledException ||
                   ex is HttpRequestException;
        }

        private static bool IsCatastrophicException(Exception ex)
        {
            // Consider certain exceptions as catastrophic and should fail the entire bulk operation
            return ex is InvalidOperationException ||
                   ex is OutOfMemoryException ||
                   ex is StackOverflowException ||
                   ex is AccessViolationException ||
                   ex is NotSupportedException;
        }

        private async Task UpdateS3ObjectMetadata(string s3Key, Dictionary<string, string> metadata)
        {
            await _s3StorageService.UpdateObjectMetadataAsync(s3Key, metadata);
        }

        /// <summary>
        /// Get all Queen gallery photos with their current metadata status
        /// </summary>
        /// <returns>List of Queen gallery photos with metadata analysis</returns>
        [HttpGet("queens/gallery")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetQueenGalleryMetadata()
        {
            try
            {
                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} retrieving Queen gallery metadata overview", adminUser?.Username);

                // Get all objects in queens/ prefix
                var queensObjects = await _s3StorageService.ListFilesAsync("queens/");
                var results = new List<object>();

                foreach (var obj in queensObjects)
                {
                    // Skip non-image files
                    if (!IsImageFile(obj.Key))
                        continue;

                    try
                    {
                        var metadata = await _s3StorageService.GetObjectMetadataDirectAsync(obj.Key);
                        var hasCustomMetadata = HasCustomCatMetadata(metadata);
                        var extractedName = ExtractCatNameFromKey(obj.Key);

                        results.Add(new
                        {
                            s3Key = obj.Key,
                            fileName = Path.GetFileName(obj.Key),
                            size = obj.Size,
                            lastModified = obj.LastModified,
                            hasCustomMetadata = hasCustomMetadata,
                            extractedCatName = extractedName,
                            currentMetadata = metadata,
                            isLikelyFallback = IsLikelyFallbackFilename(Path.GetFileName(obj.Key)),
                            needsMetadata = !hasCustomMetadata && IsLikelyFallbackFilename(Path.GetFileName(obj.Key))
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error processing Queen gallery object: {Key}", obj.Key);
                        results.Add(new
                        {
                            s3Key = obj.Key,
                            fileName = Path.GetFileName(obj.Key),
                            size = obj.Size,
                            lastModified = obj.LastModified,
                            hasCustomMetadata = false,
                            extractedCatName = ExtractCatNameFromKey(obj.Key),
                            error = "Failed to retrieve metadata"
                        });
                    }
                }

                var summary = new
                {
                    totalPhotos = results.Count,
                    photosWithCustomMetadata = results.Count(r => (bool?)r.GetType().GetProperty("hasCustomMetadata")?.GetValue(r) == true),
                    photosNeedingMetadata = results.Count(r => (bool?)r.GetType().GetProperty("needsMetadata")?.GetValue(r) == true),
                    fallbackPhotos = results.Count(r => (bool?)r.GetType().GetProperty("isLikelyFallback")?.GetValue(r) == true)
                };

                return Ok(new
                {
                    success = true,
                    message = "Queen gallery metadata overview retrieved successfully",
                    summary = summary,
                    photos = results,
                    retrievedAt = DateTime.UtcNow,
                    retrievedBy = adminUser?.Username
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving Queen gallery metadata overview");
                return StatusCode(500, new
                {
                    success = false,
                    message = "An unexpected error occurred while retrieving Queen gallery metadata",
                    errorCode = "QUEEN_GALLERY_ERROR"
                });
            }
        }

        /// <summary>
        /// Bulk update Queen gallery metadata with proper cat names
        /// </summary>
        /// <param name="updates">Queen gallery metadata updates</param>
        /// <returns>Bulk update results specifically for Queen gallery</returns>
        [HttpPost("queens/bulk-update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> BulkUpdateQueenGalleryMetadata([FromBody] List<QueenGalleryMetadataUpdate> updates)
        {
            var transactionId = Guid.NewGuid().ToString("N")[..8];
            
            try
            {
                if (!ModelState.IsValid)
                {
                    var validationErrors = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .SelectMany(x => x.Value.Errors.Select(e => $"{x.Key}: {e.ErrorMessage}"))
                        .ToList();

                    return BadRequest(new
                    {
                        success = false,
                        message = "Validation failed",
                        errors = validationErrors
                    });
                }

                if (updates == null || updates.Count == 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "No Queen gallery updates provided",
                        errorCode = "EMPTY_UPDATE_LIST"
                    });
                }

                // Validate all updates are for queens/ prefix
                var invalidUpdates = updates.Where(u => !u.S3Key.StartsWith("queens/", StringComparison.OrdinalIgnoreCase)).ToList();
                if (invalidUpdates.Any())
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "All updates must be for Queen gallery photos (queens/ prefix)",
                        errorCode = "INVALID_QUEEN_UPDATES",
                        invalidKeys = invalidUpdates.Select(u => u.S3Key)
                    });
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} performing Queen gallery bulk metadata update for {Count} photos (Transaction: {TransactionId})",
                    adminUser?.Username, updates.Count, transactionId);

                // Convert to standard metadata update format
                var standardRequests = updates.Select(update => new S3MetadataUpdateRequest
                {
                    S3Key = update.S3Key,
                    CatName = update.CatName,
                    Age = update.Age,
                    DateTaken = update.DateTaken,
                    Description = update.Description,
                    Breed = update.Breed ?? "Maine Coon",
                    Gender = update.Gender,
                    Color = update.Color,
                    Personality = update.Personality,
                    Bloodline = update.Bloodline,
                    PhotoType = update.PhotoType,
                    Tags = update.Tags,
                    BreedingStatus = "breeding-queen", // Default for Queen gallery
                    AvailabilityStatus = update.AvailabilityStatus ?? "not-for-sale"
                }).ToList();

                // Use existing bulk update functionality
                var results = new List<object>();
                var successCount = 0;
                var failedCount = 0;

                foreach (var request in standardRequests)
                {
                    var result = await ProcessSingleUpdateWithRetry(request, adminUser, transactionId);
                    results.Add(result);
                    
                    if (result.success)
                    {
                        successCount++;
                    }
                    else
                    {
                        failedCount++;
                    }

                    // Small delay to avoid rate limiting
                    await Task.Delay(100);
                }

                var responseMessage = $"Queen gallery bulk update completed: {successCount}/{updates.Count} successful";
                if (failedCount > 0)
                {
                    responseMessage += $", {failedCount} failed";
                }

                _logger.LogInformation("Queen gallery bulk update transaction {TransactionId} completed: {SuccessCount}/{Total} successful",
                    transactionId, successCount, updates.Count);

                return Ok(new
                {
                    success = true,
                    message = responseMessage,
                    transactionId = transactionId,
                    summary = new
                    {
                        total = updates.Count,
                        successful = successCount,
                        failed = failedCount,
                        successRate = Math.Round((double)successCount / updates.Count * 100, 2),
                        category = "queens"
                    },
                    results = results,
                    updatedBy = adminUser?.Username,
                    completedAt = DateTime.UtcNow,
                    nextSteps = new
                    {
                        message = "After updating S3 metadata, trigger a force re-sync to update the database",
                        endpoint = "/api/Sync/metadata/force-resync?category=queens&overwriteFallback=true"
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during Queen gallery bulk metadata update (Transaction: {TransactionId})", transactionId);
                
                return StatusCode(500, new
                {
                    success = false,
                    message = "An unexpected error occurred during Queen gallery bulk update",
                    errorCode = "QUEEN_BULK_UPDATE_ERROR",
                    transactionId = transactionId
                });
            }
        }

        private bool IsImageFile(string key)
        {
            var extension = Path.GetExtension(key).ToLowerInvariant();
            return new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp" }.Contains(extension);
        }

        private bool HasCustomCatMetadata(Dictionary<string, string> metadata)
        {
            if (metadata == null || metadata.Count == 0)
                return false;

            // Check for cat-specific metadata fields
            var catMetadataKeys = new[] { "cat-name", "name", "catname", "age", "breed", "description", "gender" };
            return catMetadataKeys.Any(key =>
                metadata.ContainsKey(key) ||
                metadata.Keys.Any(k => string.Equals(k, key, StringComparison.OrdinalIgnoreCase)));
        }

        private string ExtractCatNameFromKey(string s3Key)
        {
            var fileName = Path.GetFileNameWithoutExtension(s3Key);
            
            // Remove common prefixes
            fileName = fileName.Replace("TESTING-", "");
            fileName = fileName.Replace("IMG_", "");
            fileName = fileName.Replace("DSC_", "");
            
            // Split and take first meaningful part
            var parts = fileName.Split(new[] { '-', '_', ' ' }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (var part in parts)
            {
                if (part.All(char.IsDigit) || part.Length < 3)
                    continue;
                    
                return char.ToUpper(part[0]) + part.Substring(1).ToLower();
            }
            
            return "Unknown Queen";
        }

        private bool IsLikelyFallbackFilename(string fileName)
        {
            var lowerName = fileName.ToLowerInvariant();
            return lowerName.StartsWith("img_") ||
                   lowerName.StartsWith("dsc_") ||
                   lowerName.Contains("testing") ||
                   System.Text.RegularExpressions.Regex.IsMatch(lowerName, @"^[a-z]{3}_\d+");
        }
    }

    /// <summary>
    /// Queen gallery specific metadata update request
    /// </summary>
    public class QueenGalleryMetadataUpdate
    {
        /// <summary>
        /// S3 object key (must start with queens/)
        /// </summary>
        [Required(ErrorMessage = "S3 object key is required")]
        [RegularExpression(@"^queens/.*", ErrorMessage = "S3 key must be for Queen gallery (queens/ prefix)")]
        public string S3Key { get; set; } = string.Empty;

        /// <summary>
        /// Queen's name
        /// </summary>
        [Required(ErrorMessage = "Queen name is required")]
        [StringLength(100, ErrorMessage = "Queen name cannot exceed 100 characters")]
        public string CatName { get; set; } = string.Empty;

        /// <summary>
        /// Queen's age or life stage
        /// </summary>
        [StringLength(50, ErrorMessage = "Age cannot exceed 50 characters")]
        public string? Age { get; set; }

        /// <summary>
        /// Date photo was taken
        /// </summary>
        public string? DateTaken { get; set; }

        /// <summary>
        /// Photo description
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Queen's breed (defaults to Maine Coon)
        /// </summary>
        [StringLength(100, ErrorMessage = "Breed cannot exceed 100 characters")]
        public string? Breed { get; set; }

        /// <summary>
        /// Queen's gender (should be F for queens)
        /// </summary>
        [RegularExpression(@"^[MF]$", ErrorMessage = "Gender must be 'M' or 'F'")]
        public string? Gender { get; set; } = "F";

        /// <summary>
        /// Queen's color/pattern
        /// </summary>
        [StringLength(100, ErrorMessage = "Color cannot exceed 100 characters")]
        public string? Color { get; set; }

        /// <summary>
        /// Queen's personality
        /// </summary>
        [StringLength(500, ErrorMessage = "Personality cannot exceed 500 characters")]
        public string? Personality { get; set; }

        /// <summary>
        /// Queen's bloodline
        /// </summary>
        [StringLength(100, ErrorMessage = "Bloodline cannot exceed 100 characters")]
        public string? Bloodline { get; set; }

        /// <summary>
        /// Type of photo
        /// </summary>
        [RegularExpression(@"^(profile|action|family|breeding|growth)$", ErrorMessage = "Photo type must be one of: profile, action, family, breeding, growth")]
        public string? PhotoType { get; set; }

        /// <summary>
        /// Searchable tags
        /// </summary>
        [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
        public string? Tags { get; set; }

        /// <summary>
        /// Availability status
        /// </summary>
        [RegularExpression(@"^(available|reserved|sold|not-for-sale)$", ErrorMessage = "Availability status must be one of: available, reserved, sold, not-for-sale")]
        public string? AvailabilityStatus { get; set; }
    }

    /// <summary>
    /// S3 metadata update request model
    /// </summary>
    public class S3MetadataUpdateRequest
    {
        /// <summary>
        /// S3 object key
        /// </summary>
        [Required(ErrorMessage = "S3 object key is required")]
        [StringLength(1024, ErrorMessage = "S3 key cannot exceed 1024 characters")]
        public string S3Key { get; set; } = string.Empty;

        /// <summary>
        /// Cat name
        /// </summary>
        [StringLength(100, ErrorMessage = "Cat name cannot exceed 100 characters")]
        public string? CatName { get; set; }

        /// <summary>
        /// Cat age
        /// </summary>
        [StringLength(50, ErrorMessage = "Age cannot exceed 50 characters")]
        [RegularExpression(@"^[\d\.\-\s]*$", ErrorMessage = "Age must contain only numbers, dots, dashes, and spaces")]
        public string? Age { get; set; }

        /// <summary>
        /// Date photo was taken
        /// </summary>
        [DataType(DataType.Date)]
        public string? DateTaken { get; set; }

        /// <summary>
        /// Photo description
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Cat breed
        /// </summary>
        [StringLength(100, ErrorMessage = "Breed cannot exceed 100 characters")]
        public string? Breed { get; set; }

        /// <summary>
        /// Cat gender
        /// </summary>
        [StringLength(10, ErrorMessage = "Gender cannot exceed 10 characters")]
        [RegularExpression(@"^[MF]$", ErrorMessage = "Gender must be 'M' or 'F'")]
        public string? Gender { get; set; }

        /// <summary>
        /// Cat color/pattern
        /// </summary>
        [StringLength(100, ErrorMessage = "Color cannot exceed 100 characters")]
        public string? Color { get; set; }

        /// <summary>
        /// Cat personality
        /// </summary>
        [StringLength(500, ErrorMessage = "Personality cannot exceed 500 characters")]
        public string? Personality { get; set; }

        /// <summary>
        /// Cat bloodline
        /// </summary>
        [StringLength(100, ErrorMessage = "Bloodline cannot exceed 100 characters")]
        public string? Bloodline { get; set; }

        /// <summary>
        /// Unique cat identifier
        /// </summary>
        [StringLength(50, ErrorMessage = "Cat ID cannot exceed 50 characters")]
        [RegularExpression(@"^[a-zA-Z0-9\-_]*$", ErrorMessage = "Cat ID can only contain letters, numbers, hyphens, and underscores")]
        public string? CatId { get; set; }

        /// <summary>
        /// Official registered name
        /// </summary>
        [StringLength(200, ErrorMessage = "Registered name cannot exceed 200 characters")]
        public string? RegisteredName { get; set; }

        /// <summary>
        /// Registration number from pedigree papers
        /// </summary>
        [StringLength(100, ErrorMessage = "Registration number cannot exceed 100 characters")]
        public string? RegistrationNumber { get; set; }

        /// <summary>
        /// Father's cat ID
        /// </summary>
        [StringLength(50, ErrorMessage = "Father ID cannot exceed 50 characters")]
        [RegularExpression(@"^[a-zA-Z0-9\-_]*$", ErrorMessage = "Father ID can only contain letters, numbers, hyphens, and underscores")]
        public string? FatherId { get; set; }

        /// <summary>
        /// Mother's cat ID
        /// </summary>
        [StringLength(50, ErrorMessage = "Mother ID cannot exceed 50 characters")]
        [RegularExpression(@"^[a-zA-Z0-9\-_]*$", ErrorMessage = "Mother ID can only contain letters, numbers, hyphens, and underscores")]
        public string? MotherId { get; set; }

        /// <summary>
        /// Breeding status (available-kitten, breeding-queen, stud, retired)
        /// </summary>
        [StringLength(50, ErrorMessage = "Breeding status cannot exceed 50 characters")]
        [RegularExpression(@"^(available-kitten|breeding-queen|stud|retired)$", ErrorMessage = "Breeding status must be one of: available-kitten, breeding-queen, stud, retired")]
        public string? BreedingStatus { get; set; }

        /// <summary>
        /// Availability status (available, reserved, sold, not-for-sale)
        /// </summary>
        [StringLength(50, ErrorMessage = "Availability status cannot exceed 50 characters")]
        [RegularExpression(@"^(available|reserved|sold|not-for-sale)$", ErrorMessage = "Availability status must be one of: available, reserved, sold, not-for-sale")]
        public string? AvailabilityStatus { get; set; }

        /// <summary>
        /// Type of photo (profile, action, family, breeding, growth)
        /// </summary>
        [StringLength(50, ErrorMessage = "Photo type cannot exceed 50 characters")]
        [RegularExpression(@"^(profile|action|family|breeding|growth)$", ErrorMessage = "Photo type must be one of: profile, action, family, breeding, growth")]
        public string? PhotoType { get; set; }

        /// <summary>
        /// Age when photo was taken
        /// </summary>
        [StringLength(50, ErrorMessage = "Age at photo cannot exceed 50 characters")]
        public string? AgeAtPhoto { get; set; }

        /// <summary>
        /// Searchable tags (comma-separated)
        /// </summary>
        [StringLength(500, ErrorMessage = "Tags cannot exceed 500 characters")]
        public string? Tags { get; set; }

        /// <summary>
        /// Champion titles and achievements
        /// </summary>
        [StringLength(300, ErrorMessage = "Champion titles cannot exceed 300 characters")]
        public string? ChampionTitles { get; set; }

        /// <summary>
        /// Generation level in pedigree (1=parent, 2=grandparent, etc.)
        /// </summary>
        [StringLength(10, ErrorMessage = "Generation level cannot exceed 10 characters")]
        [RegularExpression(@"^[1-9]$", ErrorMessage = "Generation level must be a number between 1 and 9")]
        public string? GenerationLevel { get; set; }
    }
}
