#!/usr/bin/env python3
"""
Direct embedding script for MCP directory using Qwen3-8B
"""
import sys
import os
from pathlib import Path
from embed_documents import DocumentEmbedder

def main():
    print("🚀 Running MCP Directory Embedding with Qwen3-8B (4096 dimensions)")

    # Initialize embedder
    qdrant_url = os.getenv('QDRANT_URL', 'http://localhost:6333')
    embedder = DocumentEmbedder(qdrant_url)

    # Configuration
    agent = "claude"
    model = "dengcao/Qwen3-Embedding-8B:Q4_K_M"
    path = Path("/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP/MCP")
    shared = True

    print(f"📊 Configuration:")
    print(f"  Agent: {agent}")
    print(f"  Model: {model}")
    print(f"  Path: {path}")
    print(f"  Shared collections: {shared}")

    # Show current collections before
    print(f"\n📚 Current collections:")
    collections = embedder.list_collections()
    for col in sorted(collections, key=lambda x: x['name']):
        print(f"  - {col['name']}: {col['points_count']} points")

    # Process the directory
    print(f"\n🔄 Processing {path}...")

    if path.is_file():
        success = embedder.embed_file(path, agent, model, shared)
        if success:
            print("✅ File embedded successfully")
        else:
            print("❌ Failed to embed file")
    else:
        files_processed, files_success = embedder.embed_directory(path, agent, model, shared)
        print(f"✅ Processed {files_success}/{files_processed} files successfully")

    # Show final collections state
    print(f"\n📊 Final collection state:")
    collections = embedder.list_collections()
    for col in sorted(collections, key=lambda x: x['name']):
        print(f"  {col['name']}: {col['points_count']} points")

if __name__ == "__main__":
    main()