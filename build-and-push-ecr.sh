#!/usr/bin/env bash

# Build and Push Docker Images to AWS ECR
# Based on .github/workflows/frontend-ci.yml

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "[DEPRECATED] Use ./scripts/deploy/ecr-build-push.sh instead" >&2
exec ./scripts/deploy/ecr-build-push.sh "$@"
exit 0

echo -e "${BLUE}=== Docker Build and Push to AWS ECR ===${NC}"
echo -e "${YELLOW}This script will build and push all three services to AWS ECR${NC}"
echo ""

# Function to mask sensitive input
mask_input() {
    local prompt="$1"
    local var_name="$2"
    echo -n "$prompt"
    read -s value
    echo ""
    eval "$var_name='$value'"
}

# Collect AWS credentials
echo -e "${YELLOW}Please provide your AWS credentials:${NC}"
echo "(These will be used only for this session and not stored)"
echo ""

# AWS Account ID
echo -n "Enter AWS Account ID: "
read AWS_ACCOUNT_ID

# AWS Access Key ID
mask_input "Enter AWS Access Key ID: " AWS_ACCESS_KEY_ID

# AWS Secret Access Key
mask_input "Enter AWS Secret Access Key: " AWS_SECRET_ACCESS_KEY

# AWS Region (with default)
echo -n "Enter AWS Region [default: us-west-004]: "
read AWS_REGION
AWS_REGION=${AWS_REGION:-us-west-004}

# Export AWS credentials for this session
export AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY
export AWS_REGION

# Set ECR registry URL
ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

echo ""
echo -e "${GREEN}✓ AWS credentials configured${NC}"
echo -e "ECR Registry: ${ECR_REGISTRY}"
echo ""

# Authenticate with ECR
echo -e "${BLUE}Authenticating with AWS ECR...${NC}"
# Use direct docker login command for macOS compatibility
DOCKER_PASSWORD=$(aws ecr get-login-password --region ${AWS_REGION} 2>&1)

if [ $? -ne 0 ]; then
    echo -e "${RED}✗ Failed to get ECR login password${NC}"
    echo "Error: ${DOCKER_PASSWORD}"
    exit 1
fi

echo "${DOCKER_PASSWORD}" | docker login --username AWS --password-stdin ${ECR_REGISTRY} 2>&1 | grep -v "WARNING"

if [ ${PIPESTATUS[1]} -eq 0 ]; then
    echo -e "${GREEN}✓ Successfully authenticated with ECR${NC}"
else
    echo -e "${RED}✗ Failed to authenticate with ECR${NC}"
    echo "Troubleshooting tips:"
    echo "  1. Ensure Docker Desktop is running"
    echo "  2. Check AWS credentials are correct"
    echo "  3. Verify the region is correct (you entered: ${AWS_REGION})"
    exit 1
fi

# Get Git commit SHA
echo ""
echo -e "${BLUE}Getting Git commit information...${NC}"
GIT_SHA=$(git rev-parse HEAD)
GIT_SHA_SHORT=$(git rev-parse --short HEAD)
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

echo "Full SHA: ${GIT_SHA}"
echo "Short SHA: ${GIT_SHA_SHORT}"
echo "Branch: ${GIT_BRANCH}"

# Function to build and tag image
build_and_tag() {
    local service_name=$1
    local dockerfile=$2
    local context=$3

    echo ""
    echo -e "${BLUE}Building ${service_name}...${NC}"
    echo "Dockerfile: ${dockerfile}"
    echo "Context: ${context}"

    # Build with multiple tags
    docker build \
        -f ${dockerfile} \
        -t ${ECR_REGISTRY}/${service_name}:latest \
        -t ${ECR_REGISTRY}/${service_name}:${GIT_SHA_SHORT} \
        -t ${ECR_REGISTRY}/${service_name}:${GIT_BRANCH} \
        ${context}

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Successfully built ${service_name}${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to build ${service_name}${NC}"
        return 1
    fi
}

# Function to push image with all tags
push_image() {
    local service_name=$1

    echo ""
    echo -e "${BLUE}Pushing ${service_name} to ECR...${NC}"

    # Push all tags
    for tag in latest ${GIT_SHA_SHORT} ${GIT_BRANCH}; do
        echo "Pushing ${ECR_REGISTRY}/${service_name}:${tag}"
        docker push ${ECR_REGISTRY}/${service_name}:${tag}

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ Pushed ${service_name}:${tag}${NC}"
        else
            echo -e "${RED}✗ Failed to push ${service_name}:${tag}${NC}"
            return 1
        fi
    done

    return 0
}

# Build all images
echo ""
echo -e "${YELLOW}=== Building Docker Images ===${NC}"

# Build API
if ! build_and_tag "yendorcats-api" "backend/YendorCats.API/Dockerfile" "."; then
    echo -e "${RED}Failed to build API image${NC}"
    exit 1
fi

# Build Frontend (requires special handling for .git directory)
echo -e "${YELLOW}Note: Frontend build requires .git for version stamping${NC}"

# Temporarily modify .dockerignore to allow .git for frontend build
if grep -q "^\.git$" .dockerignore 2>/dev/null; then
    echo "Temporarily removing .git from .dockerignore for frontend build..."
    cp .dockerignore .dockerignore.backup
    grep -v "^\.git$" .dockerignore > .dockerignore.tmp && mv .dockerignore.tmp .dockerignore
    DOCKERIGNORE_MODIFIED=true
else
    DOCKERIGNORE_MODIFIED=false
fi

if ! build_and_tag "yendorcats-frontend" "Dockerfile.frontend.ci" "."; then
    echo -e "${RED}Failed to build Frontend image${NC}"
    # Restore .dockerignore if it was modified
    if [ "$DOCKERIGNORE_MODIFIED" = true ]; then
        mv .dockerignore.backup .dockerignore
        echo "Restored original .dockerignore"
    fi
    exit 1
fi

# Restore .dockerignore if it was modified
if [ "$DOCKERIGNORE_MODIFIED" = true ]; then
    mv .dockerignore.backup .dockerignore
    echo "Restored original .dockerignore"
fi

# Build Uploader
if ! build_and_tag "yendorcats-uploader" "tools/file-uploader/Dockerfile" "tools/file-uploader"; then
    echo -e "${RED}Failed to build Uploader image${NC}"
    exit 1
fi

# Push all images
echo ""
echo -e "${YELLOW}=== Pushing Images to ECR ===${NC}"

# Check if ECR repositories exist
echo ""
echo -e "${BLUE}Checking ECR repositories...${NC}"
for repo in yendorcats-api yendorcats-frontend yendorcats-uploader; do
    aws ecr describe-repositories --repository-names ${repo} --region ${AWS_REGION} &> /dev/null
    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}Repository ${repo} does not exist. Creating...${NC}"
        aws ecr create-repository --repository-name ${repo} --region ${AWS_REGION}
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ Created repository ${repo}${NC}"
        else
            echo -e "${RED}✗ Failed to create repository ${repo}${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✓ Repository ${repo} exists${NC}"
    fi
done

# Push API
if ! push_image "yendorcats-api"; then
    echo -e "${RED}Failed to push API image${NC}"
    exit 1
fi

# Push Frontend
if ! push_image "yendorcats-frontend"; then
    echo -e "${RED}Failed to push Frontend image${NC}"
    exit 1
fi

# Push Uploader
if ! push_image "yendorcats-uploader"; then
    echo -e "${RED}Failed to push Uploader image${NC}"
    exit 1
fi

# Display final results
echo ""
echo -e "${GREEN}=== Successfully Built and Pushed All Images ===${NC}"
echo ""
echo -e "${BLUE}Final ECR Image URIs:${NC}"
echo ""
echo "API Service:"
echo "  Latest: ${ECR_REGISTRY}/yendorcats-api:latest"
echo "  SHA: ${ECR_REGISTRY}/yendorcats-api:${GIT_SHA_SHORT}"
echo "  Branch: ${ECR_REGISTRY}/yendorcats-api:${GIT_BRANCH}"
echo ""
echo "Frontend Service:"
echo "  Latest: ${ECR_REGISTRY}/yendorcats-frontend:latest"
echo "  SHA: ${ECR_REGISTRY}/yendorcats-frontend:${GIT_SHA_SHORT}"
echo "  Branch: ${ECR_REGISTRY}/yendorcats-frontend:${GIT_BRANCH}"
echo ""
echo "Uploader Service:"
echo "  Latest: ${ECR_REGISTRY}/yendorcats-uploader:latest"
echo "  SHA: ${ECR_REGISTRY}/yendorcats-uploader:${GIT_SHA_SHORT}"
echo "  Branch: ${ECR_REGISTRY}/yendorcats-uploader:${GIT_BRANCH}"
echo ""
echo -e "${GREEN}All images are ready for deployment!${NC}"

# Clean up sensitive environment variables
unset AWS_ACCESS_KEY_ID
unset AWS_SECRET_ACCESS_KEY
