{"permissions": {"allow": ["Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__ollama__list", "<PERSON><PERSON>(curl:*)", "Read(//Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/**)", "mcp__qdrant-user__qdrant-store", "Bash(pip install:*)", "Bash(python3 -m pip install:*)", "<PERSON><PERSON>(ollama list:*)", "Bash(cd:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(for collection in yendorcats_docs qdrant-official-docs mcp_project_qwen3 ws-4c0e28638523500c shared_docs_4b claude-code central_library prefix-cache shared_code_4b)", "Read(//Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/**)"], "deny": [], "ask": []}}