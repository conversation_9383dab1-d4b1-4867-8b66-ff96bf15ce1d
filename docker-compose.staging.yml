services:
  # Frontend Service - Nginx serving static files with API proxy
  frontend:
    env_file:
      - .env.staging
    build:
      context: ./frontend
      dockerfile: Dockerfile
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/yendorcats-frontend:a247b3a
    container_name: yendorcats-frontend-staging
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=staging
    depends_on:
      - api
      - uploader
    networks:
      - yendorcats-staging
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Backend API Service - .NET Core API
  api:
    env_file:
      - .env.staging
    build:
      context: .
      dockerfile: backend/YendorCats.API/Dockerfile
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/yendorcats-api:a247b3a
    container_name: yendorcats-api-staging
    restart: unless-stopped
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - ASPNETCORE_URLS=http://+:80
      - CONTAINERIZED_BUILD=true
      - ConnectionStrings__SqliteConnection=Data Source=/app/data/yendorcats.db
      
      # AWS/B2 Configuration
      - AWS__Region=${AWS_REGION:-us-west-004}
      - AWS__UseCredentialsFromSecrets=false
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME}
      - AWS__S3__UseDirectS3Urls=true
      - AWS__S3__ServiceUrl=${AWS_S3_ENDPOINT}
      - AWS__S3__PublicUrl=${AWS_S3_PUBLIC_URL}
      - AWS__S3__UseCdn=false
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEYr
      
      # Hybrid Storage Configuration
      - HybridStorage__DefaultProvider=S3
      - HybridStorage__EnableDualStorage=true
      - HybridStorage__S3__BucketName=${AWS_S3_BUCKET_NAME}
      - HybridStorage__S3__Region=${AWS_REGION:-us-west-004}
      - HybridStorage__S3__ServiceUrl=${AWS_S3_ENDPOINT}
      - HybridStorage__S3__PublicUrl=${AWS_S3_PUBLIC_URL}
      - HybridStorage__S3__UseDirectUrls=true
      - HybridStorage__B2__BucketName=${B2_BUCKET_NAME}
      - HybridStorage__B2__ApplicationKeyId=${B2_APPLICATION_KEY_ID}
      - HybridStorage__B2__ApplicationKey=${B2_APPLICATION_KEY}
      - HybridStorage__B2__BucketId=${B2_BUCKET_ID}
      
      # JWT Configuration
      - JwtSettings__Secret=${YENDOR_JWT_SECRET}
      - JwtSettings__Issuer=YendorCatsApi
      - JwtSettings__Audience=YendorCatsClients
      - JwtSettings__ExpiryMinutes=60
      - JwtSettings__RefreshExpiryDays=7
      
      # Seeding Configuration
      - Seeding__EnableSampleData=false
      - Seeding__ForceReseed=false
      - Seeding__EnableB2Sync=true
      
      # Logging
      - Serilog__MinimumLevel__Default=Information
      - Serilog__MinimumLevel__Override__Microsoft=Warning
      - Serilog__MinimumLevel__Override__System=Warning
    volumes:
      - api-data-staging:/app/data
      - api-logs-staging:/app/Logs
    networks:
      - yendorcats-staging
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # File Uploader Service - Node.js microservice
  uploader:
    env_file:
      - .env.staging
    build:
      context: ./tools/file-uploader
      dockerfile: Dockerfile
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/yendorcats-uploader:a247b3a
    container_name: yendorcats-uploader-staging
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - NODE_ENV=staging
      - PORT=80
      
      # AWS/B2 Configuration
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
      - AWS_S3_REGION=${AWS_REGION:-us-west-004}
      - AWS_S3_ENDPOINT=${AWS_S3_ENDPOINT}
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      
      # API Configuration
      - API_BASE_URL=http://api:80
    depends_on:
      - api
    volumes:
      - uploader-temp-staging:/app/uploads
    networks:
      - yendorcats-staging
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  yendorcats-staging:
    driver: bridge
    name: yendorcats-staging-network

volumes:
  api-data-staging:
    name: yendorcats-api-data-staging
  api-logs-staging:
    name: yendorcats-api-logs-staging
  uploader-temp-staging:
    name: yendorcats-uploader-temp-staging
