{"mcpServers": {"github.com/NightTrek/Ollama-mcp": {"command": "node", "args": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP/ollama-mcp/build/index.js"], "env": {"OLLAMA_HOST": "http://127.0.0.1:11434"}, "disabled": false, "autoApprove": ["*"]}, "mcp-server-qdrant": {"command": "uvx", "args": ["mcp-server-qdrant"], "env": {"QDRANT_URL": "http://localhost:6333", "COLLECTION_NAME": "augment-memories", "EMBEDDING_MODEL": "sentence-transformers/all-MiniLM-L6-v2"}, "disabled": false, "autoApprove": ["*"]}, "brave-search": {"command": "mcp-server-brave-search", "args": [], "env": {"BRAVE_API_KEY": "YOUR_BRAVE_API_KEY_HERE"}, "disabled": false, "autoApprove": ["*"]}, "context7": {"command": "context7-mcp", "args": [], "env": {"UPSTASH_VECTOR_REST_URL": "YOUR_UPSTASH_VECTOR_URL_HERE", "UPSTASH_VECTOR_REST_TOKEN": "YOUR_UPSTASH_VECTOR_TOKEN_HERE"}, "disabled": false, "autoApprove": ["*"]}, "filesystem": {"command": "mcp-server-filesystem", "args": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects"], "env": {}, "disabled": false, "autoApprove": ["*"]}, "mcp-fetch": {"command": "mcp-fetch", "args": [], "env": {}, "disabled": false, "autoApprove": ["*"]}, "web-fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": false, "autoApprove": ["*"]}, "github": {"command": "mcp-server-github", "args": [], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "YOUR_GITHUB_TOKEN_HERE"}, "disabled": false, "autoApprove": ["*"]}, "aws-ec2": {"command": "mcp-server-aws", "args": [], "env": {"AWS_ACCESS_KEY_ID": "YOUR_AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY": "YOUR_AWS_SECRET_ACCESS_KEY", "AWS_DEFAULT_REGION": "us-east-1", "AWS_SERVICES": "ec2,s3,lambda,iam,cloudformation,rds,ecs"}, "disabled": false, "autoApprove": ["*"]}, "aws-cli": {"command": "uvx", "args": ["mcp-server-aws-cli"], "env": {"AWS_PROFILE": "default", "AWS_DEFAULT_REGION": "us-east-1"}, "disabled": false, "autoApprove": ["*"]}, "secrets-manager": {"command": "mcp-server-1password", "args": [], "env": {"OP_SERVICE_ACCOUNT_TOKEN": "YOUR_1PASSWORD_SERVICE_ACCOUNT_TOKEN"}, "disabled": false, "autoApprove": ["*"]}, "vault-secrets": {"command": "mcp-server-vault", "args": [], "env": {"VAULT_ADDR": "http://localhost:8200", "VAULT_TOKEN": "YOUR_VAULT_TOKEN_HERE", "VAULT_NAMESPACE": ""}, "disabled": false, "autoApprove": ["*"]}, "bitwarden": {"command": "mcp-server-bitwarden", "args": [], "env": {"BW_CLIENTID": "YOUR_BITWARDEN_CLIENT_ID", "BW_CLIENTSECRET": "YOUR_BITWARDEN_CLIENT_SECRET", "BW_PASSWORD": "YOUR_B<PERSON><PERSON><PERSON>EN_MASTER_PASSWORD"}, "disabled": false, "autoApprove": ["*"]}, "ollama": {"command": "node", "args": ["/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP/ollama-mcp/build/index.js"], "env": {"OLLAMA_HOST": "http://127.0.0.1:11434"}, "disabled": false, "autoApprove": ["*"]}}}