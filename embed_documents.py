#!/usr/bin/env python3
"""
Document Embedding Script for Multi-Agent Shared Context

This script creates embeddings for documents and stores them in a shared Qdrant vector database
that can be accessed by multiple AI agents (<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, etc.).

The system uses a multi-tenant approach with:
- Agent-specific collections for isolated contexts
- Shared collections for cross-agent knowledge
- Flexible embedding model support with metadata tracking
"""

import os
import sys
import hashlib
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from qdrant_strategy import QdrantMultiTenantStrategy, ContentType, CollectionConfig

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import (
        Distance, VectorParams, PointStruct, Filter, FieldCondition,
        MatchValue, Range, CollectionInfo
    )
    import ollama
except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Install with: pip install qdrant-client ollama")
    sys.exit(1)


class EmbeddingConfig:
    """Configuration for embedding models and collections using multi-tenant strategy"""

    def __init__(self):
        self.strategy = QdrantMultiTenantStrategy()

    @property
    def EMBEDDING_MODELS(self):
        return self.strategy.embedding_models

    @property
    def AGENTS(self):
        return self.strategy.agents

    def get_collection_name(self, agent: str, model: str, content_type: ContentType = ContentType.DOCS, shared: bool = True) -> str:
        """Get collection name using multi-tenant strategy"""
        return self.strategy.get_collection_for_context(agent, content_type, model, shared)

    def get_content_type_from_path(self, file_path: Path) -> ContentType:
        """Determine content type from file path"""
        suffix = file_path.suffix.lower()
        name = file_path.name.lower()

        # Code files
        if suffix in ['.py', '.js', '.ts', '.cpp', '.c', '.h', '.java', '.go', '.rs', '.rb', '.php']:
            return ContentType.CODE

        # Documentation
        if suffix in ['.md', '.rst', '.txt'] or 'readme' in name or 'doc' in name:
            return ContentType.DOCS

        # Configuration and data
        if suffix in ['.json', '.yaml', '.yml', '.toml', '.ini', '.cfg']:
            return ContentType.DOCS

        # Default to docs
        return ContentType.DOCS


class DocumentEmbedder:
    """Handles document embedding and storage in Qdrant using multi-tenant strategy"""

    def __init__(self, qdrant_url: str = "http://localhost:6333", api_key: Optional[str] = None):
        self.client = QdrantClient(url=qdrant_url, api_key=api_key)
        self.ollama_client = ollama.Client()
        self.config = EmbeddingConfig()
        self.strategy = self.config.strategy

    def get_available_models(self) -> List[str]:
        """Get available Qwen embedding models from Ollama"""
        try:
            models = self.ollama_client.list()
            available = []
            for model in models.models:  # Fixed: use .models instead of .get('models', [])
                model_name = model.model  # Fixed: use .model attribute instead of ['name']
                if model_name in self.config.EMBEDDING_MODELS:
                    available.append(model_name)
            return available
        except Exception as e:
            print(f"Error getting Ollama models: {e}")
            return []

    def create_embedding(self, text: str, model: str) -> List[float]:
        """Create embedding for text using specified model"""
        try:
            response = self.ollama_client.embeddings(model=model, prompt=text)
            return response['embedding']
        except Exception as e:
            print(f"Error creating embedding with {model}: {e}")
            return []

    def ensure_collection(self, collection_name: str, model: str) -> bool:
        """Ensure collection exists with proper configuration"""
        try:
            # Check if collection exists
            collections = self.client.get_collections().collections
            if any(col.name == collection_name for col in collections):
                return True

            # Get model dimensions
            model_config = self.config.EMBEDDING_MODELS.get(model)
            if not model_config:
                print(f"Unknown model: {model}")
                return False

            # Create collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=model_config["size"],
                    distance=Distance.COSINE
                )
            )

            # Create payload index for filtering
            self.client.create_payload_index(
                collection_name=collection_name,
                field_name="agent",
                field_schema="keyword"
            )
            self.client.create_payload_index(
                collection_name=collection_name,
                field_name="model",
                field_schema="keyword"
            )
            self.client.create_payload_index(
                collection_name=collection_name,
                field_name="file_path",
                field_schema="keyword"
            )
            self.client.create_payload_index(
                collection_name=collection_name,
                field_name="content_hash",
                field_schema="keyword"
            )

            print(f"Created collection: {collection_name}")
            return True

        except Exception as e:
            print(f"Error creating collection: {e}")
            return False

    def get_file_hash(self, file_path: Path) -> str:
        """Get SHA256 hash of file content"""
        hasher = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        return hasher.hexdigest()

    def chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Split text into overlapping chunks"""
        if len(text) <= chunk_size:
            return [text]

        chunks = []
        start = 0
        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]

            # Try to break at word boundary
            if end < len(text):
                last_space = chunk.rfind(' ')
                if last_space > chunk_size * 0.8:  # If space is not too far back
                    chunk = chunk[:last_space]
                    end = start + last_space

            chunks.append(chunk.strip())
            start = end - overlap

        return chunks

    def embed_file(self, file_path: Path, agent: str, model: str, shared: bool = True) -> bool:
        """Embed a single file and store in Qdrant"""
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            if not content.strip():
                print(f"Skipping empty file: {file_path}")
                return True

            # Get file hash to avoid duplicates
            file_hash = self.get_file_hash(file_path)

            # Determine content type and get collection name
            content_type = self.config.get_content_type_from_path(file_path)
            collection_name = self.config.get_collection_name(agent, model, content_type, shared)

            # Ensure collection exists
            if not self.ensure_collection(collection_name, model):
                return False

            # Check if file already embedded
            search_result = self.client.scroll(
                collection_name=collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="content_hash",
                            match=MatchValue(value=file_hash)
                        )
                    ]
                ),
                limit=1
            )

            if search_result[0]:  # Points found
                print(f"File already embedded: {file_path}")
                return True

            # Chunk the content
            chunks = self.chunk_text(content)

            # Create embeddings for each chunk
            points = []
            for i, chunk in enumerate(chunks):
                embedding = self.create_embedding(chunk, model)
                if not embedding:
                    continue

                point = PointStruct(
                    id=abs(hash(f"{file_hash}_{i}")) % (2**63 - 1),  # Ensure positive integer
                    vector=embedding,
                    payload={
                        "agent": agent,
                        "model": model,
                        "file_path": str(file_path),
                        "content": chunk,
                        "content_hash": file_hash,
                        "content_type": content_type.value,
                        "chunk_index": i,
                        "total_chunks": len(chunks),
                        "timestamp": datetime.now().isoformat(),
                        "file_size": len(content),
                        "file_type": file_path.suffix.lower(),
                        "shared": shared
                    }
                )
                points.append(point)

            if points:
                self.client.upsert(collection_name=collection_name, points=points)
                print(f"Embedded {len(points)} chunks from: {file_path}")

            return True

        except Exception as e:
            print(f"Error embedding file {file_path}: {e}")
            return False

    def embed_directory(self, directory: Path, agent: str, model: str, shared: bool = True, extensions: List[str] = None) -> Tuple[int, int]:
        """Embed all files in directory"""
        if extensions is None:
            extensions = ['.txt', '.md', '.py', '.js', '.ts', '.json', '.yaml', '.yml', '.cpp', '.c', '.h']

        files_processed = 0
        files_success = 0

        for file_path in directory.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in extensions:
                files_processed += 1
                if self.embed_file(file_path, agent, model, shared):
                    files_success += 1

        return files_processed, files_success

    def list_collections(self) -> List[Dict]:
        """List all collections with their stats"""
        collections = []
        try:
            for collection in self.client.get_collections().collections:
                info = self.client.get_collection(collection.name)
                collections.append({
                    "name": collection.name,
                    "points_count": info.points_count,
                    "vectors_count": info.vectors_count,
                    "status": info.status
                })
        except Exception as e:
            print(f"Error listing collections: {e}")

        return collections


def get_user_choice(prompt: str, choices: List[str]) -> str:
    """Get user choice from a list of options"""
    print(f"\n{prompt}")
    for i, choice in enumerate(choices, 1):
        print(f"{i}. {choice}")

    while True:
        try:
            selection = input(f"\nSelect (1-{len(choices)}): ").strip()
            index = int(selection) - 1
            if 0 <= index < len(choices):
                return choices[index]
            else:
                print("Invalid selection. Please try again.")
        except (ValueError, KeyboardInterrupt):
            print("Invalid input. Please enter a number.")


def main():
    """Main function"""
    print("🚀 Multi-Agent Document Embedding System")
    print("="*50)

    # Initialize embedder
    qdrant_url = os.getenv('QDRANT_URL', 'http://localhost:6333')
    qdrant_api_key = os.getenv('QDRANT_API_KEY')

    embedder = DocumentEmbedder(qdrant_url, qdrant_api_key)

    # Check available models
    available_models = embedder.get_available_models()
    if not available_models:
        print("❌ No Qwen embedding models found in Ollama")
        print("Please install models with:")
        print("  ollama pull dengcao/Qwen3-Embedding-8B:Q4_K_M")
        print("  ollama pull dengcao/Qwen3-Embedding-4B:Q4_K_M")
        sys.exit(1)

    # Get embedding config
    config = EmbeddingConfig()

    # Select embedding model
    print("\n📊 Available Qwen embedding models:")
    model_choices = [f"{model} - {config.EMBEDDING_MODELS[model]['description']}"
                    for model in available_models]
    model_choice = get_user_choice("Choose embedding model:", model_choices)
    model = model_choice.split(" - ")[0]

    # Select agent
    agents = list(config.AGENTS.keys())
    agent_descriptions = [f"{agent} - {desc}" for agent, desc in config.AGENTS.items()]
    agent_choice = get_user_choice("Choose agent context:", agent_descriptions)
    agent = agent_choice.split(" - ")[0]

    # Select sharing mode
    sharing_options = [
        "shared - Store in shared collections accessible to all agents",
        "private - Store in agent-specific collections"
    ]
    sharing_choice = get_user_choice("Choose sharing mode:", sharing_options)
    shared = sharing_choice.startswith("shared")

    # Get path to embed
    while True:
        path_input = input("\n📁 Enter path to embed (file or directory): ").strip()
        path = Path(path_input).expanduser().resolve()

        if path.exists():
            break
        else:
            print("❌ Path does not exist. Please try again.")

    # Show current collections
    collections = embedder.list_collections()
    if collections:
        print(f"\n📚 Current collections:")
        for col in collections:
            print(f"  - {col['name']}: {col['points_count']} points")

    # Show strategy info
    print(f"\n🎯 Embedding Strategy:")
    print(f"  Agent: {agent}")
    print(f"  Model: {model}")
    print(f"  Sharing: {'Shared collections' if shared else 'Private collections'}")

    # Preview collections that will be used
    if path.is_file():
        content_type = config.get_content_type_from_path(path)
        collection_name = config.get_collection_name(agent, model, content_type, shared)
        print(f"  Collection: {collection_name}")
    else:
        print(f"  Collections will be determined by file types found")

    confirm = input("Proceed? (y/N): ").strip().lower()
    if confirm != 'y':
        print("Cancelled.")
        sys.exit(0)

    # Process files
    print(f"\n🔄 Processing {path}...")

    if path.is_file():
        success = embedder.embed_file(path, agent, model, shared)
        if success:
            print("✅ File embedded successfully")
        else:
            print("❌ Failed to embed file")
    else:
        files_processed, files_success = embedder.embed_directory(path, agent, model, shared)
        print(f"✅ Processed {files_success}/{files_processed} files successfully")

    # Show final collections state
    print(f"\n📊 Final collection state:")
    collections = embedder.list_collections()
    for col in sorted(collections, key=lambda x: x['name']):
        print(f"  {col['name']}: {col['points_count']} points")


if __name__ == "__main__":
    main()