# AGENTS.md

This file provides guidance to agents when working with code in this repository.

Non‑obvious, project‑specific rules discovered from the codebase:

- Qdrant MCP server config is ENV‑ONLY; the only supported CLI flag is --transport. Use env vars for everything else (QDRANT_URL, QDRANT_LOCAL_PATH, COLLECTION_NAME, EMBEDDING_MODEL, etc.). See src/mcp_server_qdrant/main.py and settings.py.
- QDRANT_LOCAL_PATH is mutually exclusive with QDRANT_URL and QDRANT_API_KEY; settings validation will raise if combined.
- Use QDRANT_URL=":memory:" for local tests/inspector; no external Qdrant required. Collections are created automatically on first write.
- Embedding model changes alter the Qdrant named vector (“using” field) and dimension; a collection created with one provider/model will not work with another. Recreate the collection or keep the model/provider stable.
- The repo has two unrelated embedding stacks: mcp-server-qdrant uses FastEmbed (MiniLM‑L6‑v2, 384 dims by default) while top‑level scripts use <PERSON><PERSON><PERSON>wen (4096/2560/1024 dims). Do NOT write/search across these in the same collection.
- Ollama MCP server exposes SSE on /message (not /sse) in addition to stdio; port is dynamic (listen(0)) and printed on startup. OLLAMA_HOST defaults to http://127.0.0.1:11434.
- The lightweight HTTP bridge for Ollama runs on :3000 with POST /complete and GET /models, /health.

Commands (only non‑defaults):
- Run Qdrant MCP over SSE: QDRANT_URL="http://localhost:6333" COLLECTION_NAME="dev" FASTMCP_PORT=8000 uvx mcp-server-qdrant --transport sse
- Inspector (in‑memory): QDRANT_URL=":memory:" COLLECTION_NAME="test" fastmcp dev src/mcp_server_qdrant/server.py
- Single Python test (from MCP/mcp-server-qdrant): uv run -m pytest tests/test_qdrant_integration.py::test_store_and_search -q
- Lint+format via pre‑commit (ruff, ruff‑format, isort[black]): uv run pre-commit run --all-files
- Type check: uv run pyright
- Build/run Ollama MCP: cd MCP/ollama-mcp && npm install && npm run build && npm run inspector
- Start Ollama bridge: cd MCP/ollama-bridge && npm install && npm start

Code style (from configs):
- Python: ruff + ruff‑format; isort --profile black; Python ≥3.10; type‑check with both mypy and pyright (keep annotations complete).
- TypeScript: strict true, module/resolution Node16, target ES2022. Streaming tools use Web Streams (TransformStream) at runtime—requires Node ≥18.

Testing specifics:
- Pytest is async‑aware (pytest‑asyncio; asyncio_mode=auto). Tests default to in‑memory Qdrant and will download FastEmbed model on first run.