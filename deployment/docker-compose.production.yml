version: '3.8'

services:
  api:
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/yendorcats-api:a247b3a
    container_name: yendorcats-api-production
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      # AWS S3 / Backblaze Integration
      - AWS__Region=ap-southeast-2
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME}
      - AWS__S3__UseDirectS3Urls=true
      - AWS__S3__ServiceUrl=https://s3.ap-southeast-2.backblazeb2.com
      - AWS__S3__UseCdn=true
      - AWS__S3__PublicUrl=https://f004.backblazeb2.com/file/${AWS_S3_BUCKET_NAME}/{key}
      # Dynamic CDN URL based on environment or configuration
      - AWS__S3__CdnHost=https://cdn.yendorcats.com
      - AWS__S3__UseCredentialsFromSecrets=false # For production, credentials should come from Vault or IAM Role
      # Backblaze B2 Integration
      - B2_APPLICATION_KEY_ID=${B2_APPLICATION_KEY_ID}
      - B2_APPLICATION_KEY=${B2_APPLICATION_KEY}
      - B2_BUCKET_ID=${B2_BUCKET_ID}
      # Database and JWT
      - ConnectionStrings__DefaultConnection=Data Source=/app/data/yendorcats.db
      - JwtSettings__Secret=${YENDOR_JWT_SECRET}
      # CORS configuration for production frontend domain
      - CORS__AdditionalOrigins=https://yendorcats.com,http://yendorcats.com
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
    volumes:
      - api-data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - yendorcats-network

  # File Uploader Service
  uploader:
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/yendorcats-uploader:a247b3a
    container_name: yendorcats-uploader-production
    restart: unless-stopped
    environment:
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
      - AWS_S3_REGION=ap-southeast-2
      - AWS_S3_ENDPOINT=https://s3.ap-southeast-2.backblazeb2.com
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      - API_BASE_URL=http://api # Internal Docker network alias
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yendorcats-network

  # Frontend Nginx service
  frontend:
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/yendorcats-frontend:a247b3a
    container_name: yendorcats-frontend-production
    ports:
      - "80:80"
    environment:
      - NGINX_CONFIG=production
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - yendorcats-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  yendorcats-network:
    driver: bridge
