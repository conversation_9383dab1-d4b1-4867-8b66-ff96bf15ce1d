# ModSecurity Core Configuration

# Enable ModSecurity
SecRuleEngine On

# Enable request body access
SecRequestBodyAccess On

# Maximum request body size (500MB for uploads)
SecRequestBodyLimit 524288000
SecRequestBodyNoFilesLimit 131072

# Enable response body access
SecResponseBodyAccess On
SecResponseBodyMimeType text/plain text/html text/xml application/json
SecResponseBodyLimit 524288
SecResponseBodyLimitAction ProcessPartial

# Temporary files location
SecTmpDir /tmp/
SecDataDir /tmp/

# Audit logging
SecAuditEngine RelevantOnly
SecAuditLogRelevantStatus "^(?:5|4(?!04))"
SecAuditLogParts ABIJDEFHZ
SecAuditLogType Serial
SecAuditLog /var/log/modsecurity/modsec_audit.log

# Debug logging
SecDebugLog /var/log/modsecurity/modsec_debug.log
SecDebugLogLevel 3

# Set default action
SecDefaultAction "phase:1,log,auditlog,pass"
SecDefaultAction "phase:2,log,auditlog,pass"

# Process rules
SecRule REQUEST_HEADERS:Content-Type "(?:application(?:/soap\+|/)|text/)xml" \
     "id:'200000',phase:1,t:none,t:lowercase,pass,nolog,ctl:requestBodyProcessor=XML"

SecRule REQUEST_HEADERS:Content-Type "application/json" \
     "id:'200001',phase:1,t:none,t:lowercase,pass,nolog,ctl:requestBodyProcessor=JSON"

# Anomaly scoring
SecAction \
  "id:900000,\
   phase:1,\
   nolog,\
   pass,\
   t:none,\
   setvar:tx.paranoia_level=1,\
   setvar:tx.anomaly_score_threshold=5"

# Custom rules for the application
# Allow larger file uploads for the uploader service
SecRule REQUEST_URI "@beginsWith /upload/" \
    "id:1000001,\
    phase:1,\
    nolog,\
    pass,\
    ctl:requestBodyLimit=524288000"

# Whitelist API endpoints from certain checks
SecRule REQUEST_URI "@beginsWith /api/" \
    "id:1000002,\
    phase:1,\
    nolog,\
    pass,\
    ctl:ruleRemoveById=920420"
