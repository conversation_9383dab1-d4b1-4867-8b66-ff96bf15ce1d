worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Enhanced logging format for security monitoring
    log_format security '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for" '
                       'rt=$request_time uct="$upstream_connect_time" '
                       'uht="$upstream_header_time" urt="$upstream_response_time" '
                       'scheme=$scheme method=$request_method';

    access_log /var/log/nginx/access.log security;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    server_names_hash_bucket_size 64;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml application/javascript application/json;
    gzip_disable "MSIE [1-6]\.";

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline' 'unsafe-eval'; frame-ancestors 'self';" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;

    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=api:10m rate=30r/s;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;

    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=addr:10m;
    limit_conn addr 100;

    # Upstream definitions
    upstream frontend_backend {
        server frontend:80 max_fails=3 fail_timeout=30s;
    }

    upstream api_backend {
        server api:80 max_fails=3 fail_timeout=30s;
    }

    upstream uploader_backend {
        server uploader:80 max_fails=3 fail_timeout=30s;
    }

    # Block common exploits
    map $request_uri $blocked_request {
        default 0;
        ~*(/\.git|/\.env|/\.svn|/\.hg|/\.DS_Store) 1;
        ~*(phpinfo|phpmyadmin|wp-admin|wp-login) 1;
        ~*(/etc/passwd|/etc/shadow|/proc/self) 1;
    }

    # HTTP server block - Redirect to HTTPS
    server {
        listen 80;
        listen [::]:80;
        server_name sendmeit.click yendorcats.com;

        # Security: Hide nginx version
        server_tokens off;

        # Allow health checks on HTTP
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Redirect all other traffic to HTTPS
        location / {
            return 301 https://$host$request_uri;
        }
    }

    # HTTPS server block
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name sendmeit.click yendorcats.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/nginx-selfsigned.crt;
        ssl_certificate_key /etc/nginx/ssl/nginx-selfsigned.key;
        
        # SSL Security Settings
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        ssl_session_tickets off;
        ssl_stapling off;

        # Security: Hide nginx version
        server_tokens off;

        # Block requests based on map
        if ($blocked_request) {
            return 403;
        }

        # Block suspicious user agents
        if ($http_user_agent ~* (bot|crawl|spider|scan)) {
            return 403;
        }

        # Frontend root location
        location / {
            limit_req zone=general burst=20 nodelay;
            
            proxy_pass http://frontend_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_cache_bypass $http_upgrade;
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # API auth endpoints with special rate limiting
        location ~* ^/api/(login|register|auth) {
            limit_req zone=auth burst=5 nodelay;
            
            proxy_pass http://api_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_cache_bypass $http_upgrade;
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_buffering off;
        }

        # General API endpoints
        location /api/ {
            limit_req zone=api burst=50 nodelay;
            
            proxy_pass http://api_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_cache_bypass $http_upgrade;
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_buffering off;
        }

        # Upload service endpoints
        location /upload/ {
            limit_req zone=upload burst=10 nodelay;
            
            # Increase body size for uploads
            client_max_body_size 500M;
            client_body_buffer_size 1M;
            
            rewrite ^/upload/(.*) /$1 break;
            
            proxy_pass http://uploader_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_cache_bypass $http_upgrade;
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;
            proxy_buffering off;
            proxy_request_buffering off;
        }

        # Health check endpoints
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        location /api/health {
            access_log off;
            proxy_pass http://api_backend/health;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
        }

        location /upload/health {
            access_log off;
            proxy_pass http://uploader_backend/health;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
        }

        # Static file caching
        location ~* \.(jpg|jpeg|gif|png|css|js|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://frontend_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        # Security: Deny access to hidden files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
            return 404;
        }

        # Security: Deny access to backup files
        location ~* \.(bak|backup|old|orig|original|~)$ {
            deny all;
            access_log off;
            log_not_found off;
            return 404;
        }
    }
}
