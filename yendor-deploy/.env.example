# YendorCats Environment Variables Template
# Copy this file to .env and fill in your actual values
# NEVER commit the actual .env file to git!

# Database Configuration (MariaDB)
MYSQL_ROOT_PASSWORD=your_secure_root_password_here
MYSQL_DATABASE=yendorcats
MYSQL_USER=yendorcats_user
MYSQL_PASSWORD=your_secure_user_password_here

# Application Configuration
APP_ENV=production
APP_DEBUG=false
APP_KEY=your_32_character_random_string_here

# External Services (if applicable)
# API_KEY=your_api_key_here
# JWT_SECRET=your_jwt_secret_here
# SMTP_PASSWORD=your_smtp_password_here

# Docker Configuration
COMPOSE_PROJECT_NAME=yendorcats

# Uncomment and configure as needed:
# SSL_CERT_PATH=/path/to/your/cert.pem
# SSL_KEY_PATH=/path/to/your/private.key
# BACKUP_ENCRYPTION_KEY=your_backup_encryption_key
