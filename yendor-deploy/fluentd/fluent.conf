# Fluentd configuration for log collection and export

# Input from Docker containers
<source>
  @type forward
  port 24224
  bind 0.0.0.0
</source>

# Collect Docker container logs
<source>
  @type tail
  path /var/lib/docker/containers/*/*-json.log
  pos_file /fluentd/log/docker.log.pos
  tag docker.*
  read_from_head true
  <parse>
    @type json
    time_format %Y-%m-%dT%H:%M:%S.%NZ
  </parse>
</source>

# Nginx access logs
<source>
  @type tail
  path /var/log/nginx/access.log
  pos_file /fluentd/log/nginx-access.log.pos
  tag nginx.access
  <parse>
    @type nginx
  </parse>
</source>

# Nginx error logs
<source>
  @type tail
  path /var/log/nginx/error.log
  pos_file /fluentd/log/nginx-error.log.pos
  tag nginx.error
  <parse>
    @type multiline
    format_firstline /^\d{4}\/\d{2}\/\d{2}/
    format1 /^(?<time>\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}) \[(?<level>[^\]]+)\] (?<message>.*)/
  </parse>
</source>

# Add container metadata
<filter docker.**>
  @type docker_metadata
</filter>

# Add timestamp to all records
<filter **>
  @type record_transformer
  <record>
    timestamp ${time}
    hostname "#{Socket.gethostname}"
    environment "production"
  </record>
</filter>

# Buffer for batch uploads
<match **>
  @type copy
  
  # Output to local files (for backup)
  <store>
    @type file
    path /fluentd/log/output/${tag}
    compress gzip
    <buffer tag,time>
      @type file
      path /fluentd/log/buffer/
      timekey 3600  # 1 hour
      timekey_wait 10m
      chunk_limit_size 5M
      flush_interval 5m
    </buffer>
    <format>
      @type json
    </format>
  </store>
  
  # Output to S3-compatible storage (Backblaze B2)
  <store>
    @type s3
    aws_key_id "#{ENV['B2_APPLICATION_KEY_ID']}"
    aws_sec_key "#{ENV['B2_APPLICATION_KEY']}"
    s3_bucket "#{ENV['B2_BUCKET_NAME']}"
    s3_endpoint https://s3.us-west-004.backblazeb2.com
    s3_region us-west-004
    path logs/yendorcats/${tag}/%Y/%m/%d/
    <buffer tag,time>
      @type file
      path /fluentd/log/s3_buffer/
      timekey 3600  # 1 hour
      timekey_wait 10m
      chunk_limit_size 5M
      flush_interval 10m
    </buffer>
    <format>
      @type json
    </format>
    time_slice_format %Y%m%d%H
    store_as gzip
  </store>
  
  # Output to stdout for debugging
  <store>
    @type stdout
    <format>
      @type json
    </format>
  </store>
</match>
