# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a collection of Model Context Protocol (MCP) servers and tools for PaceySpace's brain-preservatives Obsidian vault and companion Notion workspace. The repository supports both PaceySpace Projects (web development) and PaceySpace Labs (CI/CD, server administration, DevOps) workflows.

## Project Structure

```
MCP/
├── ollama-mcp/           # TypeScript MCP server for Ollama integration
├── ollama-bridge/        # Express.js HTTP bridge for Ollama MCP server
├── mcp-server-qdrant/    # Python MCP server for Qdrant vector database
├── cost-explorer-mcp-server/  # AWS cost exploration server
└── cline_mcp_settings.json    # Cline configuration with auto-approve enabled
```

## Development Commands

### Ollama MCP Server (TypeScript)
```bash
cd MCP/ollama-mcp
npm install                    # Install dependencies
npm run build                  # Build TypeScript to JavaScript
npm run watch                  # Watch mode for development
npm run inspector             # Run MCP Inspector for testing
```

### Ollama Bridge (Express.js)
```bash
cd MCP/ollama-bridge
npm install                    # Install dependencies
npm start                     # Start the HTTP bridge server
```

### Qdrant MCP Server (Python)
```bash
cd MCP/mcp-server-qdrant
# Using uv/uvx (recommended):
uvx mcp-server-qdrant                                    # Run directly
QDRANT_URL="http://localhost:6333" uvx mcp-server-qdrant  # With Qdrant URL
QDRANT_URL=":memory:" uvx mcp-server-qdrant              # In-memory mode
fastmcp dev src/mcp_server_qdrant/server.py             # Development mode with inspector

# Testing and development:
pytest                        # Run tests
ruff check                    # Lint code
pyright                       # Type checking
isort .                       # Import sorting
```

## Architecture Overview

### Ollama MCP Server
- **Language**: TypeScript with Node.js
- **Framework**: @modelcontextprotocol/sdk
- **Purpose**: Bridge between MCP clients and Ollama's local LLM API
- **Transport**: Both stdio and SSE (Server-Sent Events) supported
- **Key Features**: Full Ollama API coverage, OpenAI-compatible chat completion, model management tools

### Qdrant MCP Server
- **Language**: Python
- **Framework**: FastMCP (built on top of MCP SDK)
- **Purpose**: Semantic memory layer using Qdrant vector search
- **Transport**: stdio, SSE, and streamable-http
- **Key Features**: Store and retrieve information semantically, configurable embedding models

### Ollama Bridge
- **Language**: JavaScript with Express.js
- **Purpose**: HTTP bridge for Ollama MCP server autocomplete functionality
- **Use Case**: Enables web-based clients to access Ollama MCP features

## Configuration

### Environment Variables

#### Ollama MCP Server
- `OLLAMA_HOST`: Ollama API endpoint (default: http://127.0.0.1:11434)

#### Qdrant MCP Server
- `QDRANT_URL`: Qdrant server URL (required, or use QDRANT_LOCAL_PATH)
- `QDRANT_LOCAL_PATH`: Local Qdrant database path (alternative to QDRANT_URL)
- `QDRANT_API_KEY`: API key for managed Qdrant instances
- `COLLECTION_NAME`: Default collection name for operations
- `EMBEDDING_MODEL`: Embedding model (default: sentence-transformers/all-MiniLM-L6-v2)
- `FASTMCP_PORT`: Port for SSE transport (default: 8000)
- `FASTMCP_HOST`: Host binding (default: 127.0.0.1, use 0.0.0.0 for Docker)

### MCP Client Configuration

#### Claude Desktop (macOS)
Configuration file: `~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "ollama": {
      "command": "node",
      "args": ["/path/to/ollama-mcp/build/index.js"],
      "env": {
        "OLLAMA_HOST": "http://127.0.0.1:11434"
      }
    },
    "qdrant": {
      "command": "uvx",
      "args": ["mcp-server-qdrant"],
      "env": {
        "QDRANT_URL": "http://localhost:6333",
        "COLLECTION_NAME": "your-collection-name"
      }
    }
  }
}
```

#### VS Code MCP
Create `.vscode/mcp.json`:

```json
{
  "inputs": [
    {"type": "promptString", "id": "qdrantUrl", "description": "Qdrant URL"},
    {"type": "promptString", "id": "qdrantApiKey", "description": "Qdrant API Key", "password": true},
    {"type": "promptString", "id": "collectionName", "description": "Collection Name"}
  ],
  "servers": {
    "qdrant": {
      "command": "uvx",
      "args": ["mcp-server-qdrant"],
      "env": {
        "QDRANT_URL": "${input:qdrantUrl}",
        "QDRANT_API_KEY": "${input:qdrantApiKey}",
        "COLLECTION_NAME": "${input:collectionName}"
      }
    }
  }
}
```

## Testing and Development

### MCP Inspector
Both servers support the MCP Inspector for testing:

```bash
# Ollama MCP Server
cd MCP/ollama-mcp && npm run inspector

# Qdrant MCP Server
QDRANT_URL=":memory:" COLLECTION_NAME="test" fastmcp dev src/mcp_server_qdrant/server.py
```

Open http://localhost:5173 to access the inspector interface.

### Transport Protocols
- **stdio**: Default for local MCP clients (Claude Desktop, local tools)
- **sse**: Server-Sent Events for remote clients and web interfaces
- **streamable-http**: Modern HTTP streaming for remote clients

## Common Workflows

### Setting up Qdrant for Local Development
```bash
# Start local Qdrant with Docker
docker run -p 6333:6333 qdrant/qdrant

# Test connection
curl -sS http://localhost:6333/healthz

# Run MCP server with local Qdrant
QDRANT_URL="http://localhost:6333" COLLECTION_NAME="dev" uvx mcp-server-qdrant
```

### Ollama Model Management
```bash
# Install a model (via MCP or direct CLI)
ollama pull llama2
ollama list
ollama run llama2 "Hello world"
```

## Troubleshooting

### Qdrant Connection Issues
- Verify Qdrant is running: `curl http://localhost:6333/healthz`
- Use in-memory mode for testing: `QDRANT_URL=":memory:"`
- Don't set both QDRANT_URL and QDRANT_LOCAL_PATH

### Ollama Connection Issues
- Check Ollama server status: `ollama list`
- Verify OLLAMA_HOST environment variable
- Default port is 11434

### MCP Client Issues
- Restart the MCP client after configuration changes
- Check server logs for detailed error messages
- Verify file paths and permissions in configuration

## Documentation Reference

The repository includes comprehensive documentation for each component:
- `MCP/ollama-mcp/README.md`: Ollama MCP server details
- `MCP/mcp-server-qdrant/README.md`: Qdrant MCP server documentation
- `AGENTS.MD`: AI agent guidelines and MCP integration context
- `agent-configuration-guide.md`: Multi-agent workflow configuration

## Related Resources

This repository is part of the PaceySpace brain-preservatives ecosystem:
- Organization: PaceySpace (Projects + Labs)
- Infrastructure: Self-hosted on-prem bare-metal clusters
- Documentation system: Obsidian vault with PARA organization method
- Task management: Todoist + Notion integration