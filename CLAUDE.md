# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
```bash
# Backend API (.NET 8)
cd backend/YendorCats.API
dotnet restore              # Restore packages
dotnet run                  # Start API (dev mode) - https://localhost:5001, http://localhost:5000
dotnet watch                # Start with hot reload
dotnet build                # Build without running
dotnet test                 # Run unit tests

# File Uploader Service (Node.js)
cd tools/file-uploader
npm install                 # Install dependencies
npm start                   # Start uploader service - http://localhost:5002

# Frontend (served by backend in dev)
# Frontend files are in frontend/ and served by the backend API
# Access at http://localhost:5000 when backend is running
# No build step required - vanilla JavaScript
```

### Docker Development
```bash
# Local development with Docker
make build-dev              # Build for local development
make up-dev                 # Start development containers
make down-dev               # Stop development containers

# Production-like local testing
make local-test-setup       # Setup local testing environment
make local-test             # Deploy local testing (mirrors production)
make local-test-stop        # Stop local testing
make local-test-logs        # View logs
make local-test-health      # Run health checks
```

### Production Deployment (ECR)
```bash
make deploy                 # Full ECR deployment: build, tag, push
make ecr-login              # Login to ECR
make build                  # Build services
make push                   # Push to ECR
make run-ecr                # Run using ECR images
```

### Database Management
```bash
# Entity Framework migrations
cd backend/YendorCats.API
dotnet ef migrations add <MigrationName>    # Add migration
dotnet ef database update                   # Apply migrations

# Database is SQLite by default (Data/yendorcats.db)
# MariaDB configuration available but commented out in Program.cs
```

### Testing
```bash
# Backend tests (uses xUnit, Moq, FluentAssertions)
cd backend
dotnet test                                    # Run all tests
dotnet test --filter "FullyQualifiedName~ServiceTests"  # Run specific test class
dotnet test --collect:"XPlat Code Coverage"   # Run with coverage

# Integration tests
cd backend/YendorCats.Tests
dotnet test --filter Category=Integration

# Local testing environment (production mirror)
./scripts/deploy-local-test.sh deploy
./scripts/deploy-local-test.sh health

# Solution-level commands
dotnet test yendorcats.sln                    # Run all tests in solution
```

## Architecture Overview

### High-Level Architecture
The project is a **microservices-based exotic cat breeder website** with:
- **.NET 8 Web API** (backend) serving both API and static frontend
- **Node.js File Uploader** microservice for image uploads with metadata
- **Vanilla JavaScript frontend** (no frameworks) with responsive design
- **SQLite database** for data persistence (with MariaDB support available)
- **Backblaze B2 storage** (S3-compatible) for image files with rich metadata

### Core Components

#### Backend API (`backend/YendorCats.API/`)
- **Program.cs**: Main application startup, DI container, middleware pipeline
- **Controllers/**: REST API endpoints (Gallery, PhotoUpload, Auth, Cats, Users)
- **Services/**: Business logic layer with interfaces and implementations
- **Data/**: Entity Framework DbContext, repositories, models
- **Middleware/**: Custom middleware (error handling, rate limiting, security headers)
- **Configuration/**: App settings, JWT, storage provider configurations

#### Frontend (`frontend/`)
- **Vanilla JavaScript** with modern ES6+ features
- **Performance optimized** with smart image loading, caching
- **Mobile-first responsive design**
- Key files:
  - `js/gallery-v2.js`: Main gallery functionality
  - `js/smart-image-loader.js`: Optimized image loading
  - `js/api-optimization.js`: API caching and optimization

#### File Uploader (`tools/file-uploader/`)
- **Express.js** microservice for handling file uploads
- **Multer** for multipart form handling
- **AWS SDK** for S3-compatible storage (Backblaze B2)
- **Rich metadata** collection and storage as S3 object metadata

### Key Services Architecture

#### Storage Architecture
- **Primary**: S3-compatible object storage (Backblaze B2) with metadata
- **Database**: SQLite for relational data, metadata indexing
- **Hybrid approach**: S3 for files/metadata, DB for relationships and indexing

#### Authentication & Security
- **JWT-based** authentication with refresh tokens
- **Role-based authorization** (admin/user roles)
- **Rate limiting** middleware with configurable limits
- **CORS policies** environment-specific (permissive dev, restrictive prod)
- **Security headers** middleware for production hardening

#### Caching Strategy
- **Multi-tier caching**: Response caching, distributed cache, memory cache
- **Gallery caching**: Smart caching for image metadata and thumbnails
- **Background services**: Automatic cache warmup and metadata sync

### Database Schema
- **SQLite primary** with Entity Framework Core
- **Cat profiles**: Individual cat information and photo relationships
- **Gallery images**: Metadata index with S3 object references
- **Users/Auth**: User management, roles, refresh tokens
- **Migration support**: EF Core migrations for schema changes

### Deployment Architecture
- **Docker containerized** services
- **Production**: ECR registry, container orchestration
- **Local testing**: Full production mirror with docker-compose
- **Environment management**: .env files, secrets management via SecretsManagerService

### Image Management System
- **S3 object metadata**: Primary metadata storage method
- **Categories**: studs/, queens/, kittens/, gallery/ organization
- **Filename parsing**: Fallback for legacy images
- **Format**: `[cat-name]-[age]-[date(DDMMYY)]-[order].jpg`
- **Search/filtering**: Advanced metadata-based search capabilities

### Configuration Management
- **Environment-based** configuration (Development/Staging/Production)
- **Secrets management**: SecretsManagerService with multiple fallbacks
- **JWT settings**: Configurable via environment variables
- **Storage providers**: Multiple S3-compatible provider support
- **CORS origins**: Dynamic origin configuration

### Background Services
- **MetadataSyncBackgroundService**: Syncs S3 metadata to database
- **PhotoIndexBackgroundService**: Maintains search indexes
- **Cache warmup**: Preloads frequently accessed data

### API Design Patterns
- **Repository pattern**: Data access abstraction
- **Service layer**: Business logic separation
- **Dependency injection**: Constructor-based DI throughout
- **Async/await**: Consistent async pattern for I/O operations
- **Error handling**: Centralized middleware with proper HTTP status codes

## Important Notes

### Environment Configuration
- **Development**: Uses `appsettings.Development.json` and local SQLite
- **Production**: Uses environment variables and container-mounted database
- **Secrets**: Hierarchical fallback (SecretsManager → env vars → config files)

### Frontend-Backend Integration
- **Backend serves frontend** in development (SPA fallback routing)
- **Containerized mode**: Frontend served separately via Nginx
- **API communication**: RESTful endpoints with proper error handling

### File Upload Flow
1. Frontend/Uploader → File Uploader Service (Node.js)
2. File Uploader → Backblaze B2 with metadata
3. File Uploader → Backend API for database indexing
4. Backend → Updates search indexes and cache

## Development Workflow Specifics

### Environment Setup Requirements
- **.NET 8 SDK** for backend development
- **Node.js 18+** for file uploader service
- **Backblaze B2 credentials** for S3-compatible storage
- **Docker Desktop** for containerized development/testing

### Configuration Hierarchy
Environment configurations are resolved in this order:
1. **SecretsManagerService** (production)
2. **Environment variables** (`AWS_S3_ACCESS_KEY`, `YENDOR_JWT_SECRET`, etc.)
3. **appsettings files** (`appsettings.Development.json`)
4. **Fallback defaults** (development only)

### Frontend Development Notes
- **No build process** - uses vanilla JavaScript, HTML, CSS
- **Hot reload** - backend automatically serves updated frontend files
- **File watcher** - backend watches `frontend/` directory for changes
- **Mobile-first** responsive design approach

This architecture supports rapid development while maintaining production-ready scalability and security.

## Context Management & Memory

### Qdrant Integration
The project uses Qdrant vector database for storing critical development context and memories:

- **Context Storage**: Use `mcp__qdrant-user__qdrant-store` to save important development decisions, architectural changes, and critical tasks
- **Context Retrieval**: Use `mcp__qdrant-user__qdrant-find` to search previous context and decisions
- **Memory Persistence**: All major development milestones, bug fixes, and architectural decisions should be stored in Qdrant for future reference

#### Local Qdrant (dev) usage
- Service: Docker compose at `docker-compose.qdrant.yml`
- Data: Docker named volume `yendorcats_qdrant_data` (not in repo)
- Do NOT bind-mount `./qdrant_storage` — this repo should not contain vector store data

Quick commands:

```bash
# Start/stop
make qdrant-up
make qdrant-down

# Health and collections
make qdrant-health
make qdrant-collections
```

### Critical Tasks Storage Protocol
When working on the yendorcats project, always store:
1. **Architectural decisions** and their rationale
2. **Major bug fixes** and their solutions
3. **Database schema changes** and migration notes
4. **API endpoint modifications** and breaking changes
5. **Deployment configuration changes** and environment updates
6. **Security implementations** and authentication updates
7. **Performance optimizations** and their impact measurements

### Context Engine Status
- **Project Location**: `/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src`
- **Embedding Process**: Background embedding initialized for project files
- **Memory Collection**: `claude-code` collection in Qdrant
- **Last Context Update**: Project structure and architecture overview stored