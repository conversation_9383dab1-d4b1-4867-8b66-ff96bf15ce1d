# WARP.md

This file provides guidance to <PERSON><PERSON> (warp.dev) when working with code in this repository.

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Warp quick commands (copy/paste)
```bash
# Start full dev stack via Docker
make build-dev && make up-dev

# Backend API (.NET 8) - dev loop
cd backend/YendorCats.API
DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1 dotnet watch

# Run a single backend test (by name/class)
cd ../
dotnet test --filter "FullyQualifiedName~ServiceTests"    # class
# or a specific test
# dotnet test --filter "FullyQualifiedName=Namespace.ClassName.MethodName"

# File uploader (Node)
cd ../../tools/file-uploader
npm install && npm start

# Local production-like test
cd ../../
make local-test-setup && make local-test && make local-test-health

# Qdrant (context store) for dev
make qdrant-up && make qdrant-health
```

### Core Development
```bash
# Backend API (.NET 8)
cd backend/YendorCats.API
dotnet restore              # Restore packages
dotnet run                  # Start API (dev mode) - https://localhost:5001, http://localhost:5000
dotnet watch                # Start with hot reload
dotnet build                # Build without running
dotnet test                 # Run unit tests

# File Uploader Service (Node.js)
cd tools/file-uploader
npm install                 # Install dependencies
npm start                   # Start uploader service - http://localhost:5002

# Frontend (served by backend in dev)
# Frontend files are in frontend/ and served by the backend API
# Access at http://localhost:5000 when backend is running
# No build step required - vanilla JavaScript
```

### Docker Development
```bash
# Local development with Docker
make build-dev              # Build for local development
make up-dev                 # Start development containers
make down-dev               # Stop development containers
make status                 # Show status of services
make logs                   # Tail logs for services
```

### S3/B2 metadata operations (Backblaze via S3 API)
```bash
# Set up environment for scripts (uses .env or env vars)
python3 -m venv .venv && source .venv/bin/activate
python -m pip install --upgrade pip -r requirements.txt

# Configure AWS CLI profile for B2 (one-time)
aws configure --profile b2-yendorcats
export AWS_PROFILE=b2-yendorcats
export AWS_DEFAULT_REGION=us-west-004

# List buckets/objects against B2 S3 endpoint
aws --endpoint-url https://s3.us-west-004.backblazeb2.com s3 ls
aws --endpoint-url https://s3.us-west-004.backblazeb2.com s3 ls s3://yendor --recursive | head -20

# View metadata for a specific object
aws --endpoint-url https://s3.us-west-004.backblazeb2.com s3api head-object \
  --bucket yendor \
  --key YendorCats-General-SiteAccess/queens/IMG_8520.jpg | jq '.Metadata'

# Update metadata in-place (copy-object with REPLACE)
aws --endpoint-url https://s3.us-west-004.backblazeb2.com s3api copy-object \
  --bucket yendor \
  --copy-source yendor/YendorCats-General-SiteAccess/queens/IMG_8520.jpg \
  --key YendorCats-General-SiteAccess/queens/IMG_8520.jpg \
  --metadata-directive REPLACE \
  --metadata name=Jeannie,gender=F,breed="Maine Coon",category=queens

# Python helper to set and verify metadata
python set_s3_metadata.py

# API smoke test for metadata in responses
./test-s3-metadata.sh
```

# Production-like local testing
make local-test-setup       # Setup local testing environment
make local-test             # Deploy local testing (mirrors production)
make local-test-stop        # Stop local testing
make local-test-logs        # View logs
make local-test-health      # Run health checks
```

### Production Deployment (ECR)
```bash
make deploy                 # Full ECR deployment: build, tag, push
make ecr-login              # Login to ECR
make build                  # Build services
make push                   # Push to ECR
make run-ecr                # Run using ECR images

# Advanced deployment scripts (deployment-scripts/)
./deployment-scripts/deploy/build-and-push.sh       # Build and push to ECR
./deployment-scripts/deploy/deploy-staging.sh       # Deploy to staging environment
./deployment-scripts/deploy/deploy-production.sh    # Deploy to production environment
./deployment-scripts/server/pull-and-deploy.sh      # Server-side deployment script
```

### Database Management
```bash
# Entity Framework migrations
cd backend/YendorCats.API
dotnet ef migrations add <MigrationName>    # Add migration
dotnet ef database update                   # Apply migrations

# Database is SQLite by default (Data/yendorcats.db)
# MariaDB configuration available but commented out in Program.cs
```

### Testing
```bash
# Backend tests (uses xUnit, Moq, FluentAssertions)
cd backend
dotnet test                                    # Run all tests
dotnet test --filter "FullyQualifiedName~ServiceTests"  # Run specific test class
dotnet test --collect:"XPlat Code Coverage"   # Run with coverage

# Integration tests
cd backend/YendorCats.Tests
dotnet test --filter Category=Integration

# Local testing environment (production mirror)
./scripts/deploy-local-test.sh deploy
./scripts/deploy-local-test.sh health

# Solution-level commands
dotnet test yendorcats.sln                    # Run all tests in solution
```

## Architecture Overview

### High-Level Architecture
The project is a **microservices-based exotic cat breeder website** with:
- **.NET 8 Web API** (backend) serving both API and static frontend
- **Node.js File Uploader** microservice for image uploads with metadata
- **Vanilla JavaScript frontend** (no frameworks) with responsive design
- **SQLite database** for data persistence (with MariaDB support available)
- **Backblaze B2 storage** (S3-compatible) for image files with rich metadata

### Core Components

#### Backend API (`backend/YendorCats.API/`)
- **Program.cs**: Main application startup, DI container, middleware pipeline
- **Controllers/**: REST API endpoints (Gallery, PhotoUpload, Auth, Cats, Users)
- **Services/**: Business logic layer with interfaces and implementations
- **Data/**: Entity Framework DbContext, repositories, models
- **Middleware/**: Custom middleware (error handling, rate limiting, security headers)
- **Configuration/**: App settings, JWT, storage provider configurations

#### Frontend (`frontend/`)
- **Vanilla JavaScript** with modern ES6+ features
- **Performance optimized** with smart image loading, caching
- **Mobile-first responsive design**
- Key files:
  - `js/gallery-v2.js`: Main gallery functionality
  - `js/smart-image-loader.js`: Optimized image loading
  - `js/api-optimization.js`: API caching and optimization

#### File Uploader (`tools/file-uploader/`)
- **Express.js** microservice for handling file uploads
- **Multer** for multipart form handling
- **AWS SDK** for S3-compatible storage (Backblaze B2)
- **Rich metadata** collection and storage as S3 object metadata

### Key Services Architecture

#### Storage Architecture
- **Primary**: S3-compatible object storage (Backblaze B2) with metadata
- **Database**: SQLite for relational data, metadata indexing
- **Hybrid approach**: S3 for files/metadata, DB for relationships and indexing

#### Authentication & Security
- **JWT-based** authentication with refresh tokens
- **Role-based authorization** (admin/user roles)
- **Rate limiting** middleware with configurable limits
- **CORS policies** environment-specific (permissive dev, restrictive prod)
- **Security headers** middleware for production hardening

#### Caching Strategy
- **Multi-tier caching**: Response caching, distributed cache, memory cache
- **Gallery caching**: Smart caching for image metadata and thumbnails
- **Background services**: Automatic cache warmup and metadata sync

### Database Schema
- **SQLite primary** with Entity Framework Core
- **Cat profiles**: Individual cat information and photo relationships
- **Gallery images**: Metadata index with S3 object references
- **Users/Auth**: User management, roles, refresh tokens
- **Migration support**: EF Core migrations for schema changes

### Deployment Architecture
- **Docker containerized** services
- **Production**: ECR registry, container orchestration
- **Local testing**: Full production mirror with docker-compose
- **Environment management**: .env files, secrets management via SecretsManagerService

### Image Management System
- **S3 object metadata**: Primary metadata storage method
- **Categories**: studs/, queens/, kittens/, gallery/ organization
- **Filename parsing**: Fallback for legacy images
- **Format**: `[cat-name]-[age]-[date(DDMMYY)]-[order].jpg`
- **Search/filtering**: Advanced metadata-based search capabilities

Key metadata keys used by the app (as recognized in scripts/backends):
- name/cat-name (required for display), gender, breed, category, age, date-taken, description, tags
Conventions for Backblaze B2:
- User metadata keys are normalized lowercase; prefer hyphenated keys (e.g., date-taken)
- Updates require copy-object with MetadataDirective=REPLACE; provide all desired keys each time

### Configuration Management
- **Environment-based** configuration (Development/Staging/Production)
- **Secrets management**: SecretsManagerService with multiple fallbacks
- **JWT settings**: Configurable via environment variables
- **Storage providers**: Multiple S3-compatible provider support
- **CORS origins**: Dynamic origin configuration

### Background Services
- **MetadataSyncBackgroundService**: Syncs S3 metadata to database
- **PhotoIndexBackgroundService**: Maintains search indexes
- **Cache warmup**: Preloads frequently accessed data

### API Design Patterns
- **Repository pattern**: Data access abstraction
- **Service layer**: Business logic separation
- **Dependency injection**: Constructor-based DI throughout
- **Async/await**: Consistent async pattern for I/O operations
- **Error handling**: Centralized middleware with proper HTTP status codes

## Important Notes

### Environment Configuration
- **Development**: Uses `appsettings.Development.json` and local SQLite
- **Production**: Uses environment variables and container-mounted database
- **Secrets**: Hierarchical fallback (SecretsManager → env vars → config files)

### Frontend-Backend Integration
- **Backend serves frontend** in development (SPA fallback routing)
- **Containerized mode**: Frontend served separately via Nginx
- **API communication**: RESTful endpoints with proper error handling

### File Upload Flow
1. Frontend/Uploader → File Uploader Service (Node.js)
2. File Uploader → Backblaze B2 with metadata
3. File Uploader → Backend API for database indexing
4. Backend → Updates search indexes and cache

## Development Workflow Specifics

### Environment Setup Requirements
- **.NET 8 SDK** for backend development
- **Node.js 18+** for file uploader service
- **Backblaze B2 credentials** for S3-compatible storage
- **Docker Desktop** for containerized development/testing

### Configuration Hierarchy
Environment configurations are resolved in this order:
1. **SecretsManagerService** (production)
2. **Environment variables** (`AWS_S3_ACCESS_KEY`, `YENDOR_JWT_SECRET`, etc.)
3. **appsettings files** (`appsettings.Development.json`)
4. **Fallback defaults** (development only)

### Frontend Development Notes
- **No build process** - uses vanilla JavaScript, HTML, CSS
- **Hot reload** - backend automatically serves updated frontend files
- **File watcher** - backend watches `frontend/` directory for changes
- **Mobile-first** responsive design approach

This architecture supports rapid development while maintaining production-ready scalability and security.

## Context Management & Memory

### Qdrant Integration
The project uses Qdrant vector database for storing critical development context and memories:

- **Context Storage**: Use `mcp__qdrant-user__qdrant-store` to save important development decisions, architectural changes, and critical tasks
- **Context Retrieval**: Use `mcp__qdrant-user__qdrant-find` to search previous context and decisions
- **Memory Persistence**: All major development milestones, bug fixes, and architectural decisions should be stored in Qdrant for future reference

#### Local Qdrant (dev) usage
- Service: Docker compose at `docker-compose.qdrant.yml`
- Data: Docker named volume `yendorcats_qdrant_data` (not in repo)
- Do NOT bind-mount `./qdrant_storage` — this repo should not contain vector store data

Quick commands:

```bash
# Start/stop
make qdrant-up
make qdrant-down

# Health and collections
make qdrant-health
make qdrant-collections
```

### Critical Tasks Storage Protocol
When working on the yendorcats project, always store:
1. **Architectural decisions** and their rationale
2. **Major bug fixes** and their solutions
3. **Database schema changes** and migration notes
4. **API endpoint modifications** and breaking changes
5. **Deployment configuration changes** and environment updates
6. **Security implementations** and authentication updates
7. **Performance optimizations** and their impact measurements

### Context Engine Status
- **Project Location**: `/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src`
- **Embedding Process**: Background embedding initialized for project files
- **Memory Collection**: `claude-code` collection in Qdrant
- **Last Context Update**: Project structure and architecture overview stored

## Multi-Agent Coordination System

### Agent Collaboration Protocol
The project includes a multi-agent coordination system to prevent conflicts when multiple AI agents work on the codebase simultaneously:

- **Coordination File**: `AGENT-COLLABORATION.md` serves as real-time coordination hub
- **Task Declaration**: Agents declare active work areas to prevent conflicts
- **Real-time Updates**: File-based coordination with embedding integration
- **Conflict Prevention**: Agents check active tasks before starting work

### Agent Guidelines
- **Before starting work**: Check `AGENT-COLLABORATION.md` for conflicting tasks
- **During work**: Add entry to Current Active Tasks section
- **After completion**: Remove entry and update context in Qdrant
- **Context sharing**: Store architectural decisions and bug fixes in Qdrant memory

## Python & Shell Script Reference

### Python Scripts for S3/B2 Metadata Management

#### set_s3_metadata.py
Updates S3 metadata for cat images stored in Backblaze B2.
```bash
# Usage (interactive, reads from .env)
python set_s3_metadata.py

# Environment variables used:
# B2_APPLICATION_KEY_ID, B2_APPLICATION_KEY, AWS_S3_BUCKET_NAME
# AWS__S3__KeyPrefix (defaults to 'YendorCats-General-SiteAccess/')
```

#### embed_content.py
Generates embeddings for project files using Qwen3 model.
```bash
# Full content embedding
python embed_content.py

# Smoke test
python embed_smoke.py
```

#### test-s3-connection.py, test-s3-credentials.py
Test S3/B2 connectivity and credentials.
```bash
python test-s3-connection.py
python test-s3-credentials.py
```

### Shell Scripts

#### test-s3-metadata.sh
Tests API endpoints for S3 metadata presence.
```bash
./test-s3-metadata.sh
# Checks localhost:5003 API endpoints for metadata fields
```

#### Local Testing and Deployment Scripts
```bash
# Local testing (production mirror)
./scripts/setup-local-test.sh      # One-time setup
./scripts/deploy-local-test.sh deploy
./scripts/deploy-local-test.sh health
./scripts/deploy-local-test.sh stop

# ECR deployment
./build-and-push-ecr.sh             # Build and push to AWS ECR
./ci-deploy.sh                       # CI/CD deployment
./deploy-to-ecr.sh                   # Deploy using ECR images
```

## Troubleshooting Guide

### S3/B2 Common Issues

**InvalidAccessKeyId or SignatureDoesNotMatch**
- Verify B2 credentials in .env: `B2_APPLICATION_KEY_ID` and `B2_APPLICATION_KEY`
- Confirm region is `us-west-004`
- Test with: `aws --endpoint-url https://s3.us-west-004.backblazeb2.com s3 ls`

**Metadata Not Visible After Update**
- B2 normalizes user metadata keys to lowercase
- Use copy-object with MetadataDirective=REPLACE to ensure all keys are provided
- Verify with: `aws s3api head-object --bucket yendor --key YOUR_KEY --endpoint-url https://s3.us-west-004.backblazeb2.com`

**"No Such Key" Errors**
- Check exact key path with: `aws s3 ls s3://yendor/YendorCats-General-SiteAccess/ --recursive --endpoint-url https://s3.us-west-004.backblazeb2.com`
- Verify prefix requirements in B2 application key settings

### Development Environment Issues

**Backend API Not Starting**
- Check .env file exists with required DB and S3 credentials
- Try: `cd backend/YendorCats.API && dotnet restore && dotnet run`
- For M1/M2 Macs: `DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1 dotnet run`

**Docker Containers Not Starting**
- Ensure OrbStack is running (preferred over Docker Desktop)
- Check port conflicts: `make status` or `docker ps`
- Review logs: `make logs` or `docker-compose logs -f`

**File Uploader Service Issues**
- Verify Node.js 18+ is installed
- Check environment variables in tools/file-uploader/.env
- Test with: `cd tools/file-uploader && npm install && npm start`

**Database Connection Issues**
- For SQLite (default): Check if Data/yendorcats.db exists
- For MariaDB: Verify connection string and database exists
- Run migrations: `cd backend/YendorCats.API && dotnet ef database update`

### Testing and Validation Issues

**Tests Failing**
- Run specific test: `dotnet test --filter "FullyQualifiedName~YourTestName"`
- Check test database isolation and cleanup
- Verify mock configurations in test setup

**Local Test Environment Issues**
- Reset: `make local-test-stop && make local-test-setup && make local-test`
- Check health: `make local-test-health`
- Review logs: `make local-test-logs`

## End-to-End S3 B2 Metadata Workflow

Complete workflow for managing cat images from local files to website display:

### 1. Prepare Local Images
```bash
# Organize images in local directories (example)
mkdir -p local-photos/{queens,studs,kittens}
# Copy images with descriptive names: [cat-name]-[age]-[date]-[sequence].jpg
```

### 2. Upload to B2 via S3 API
```bash
# Upload batch of images
aws --endpoint-url https://s3.us-west-004.backblazeb2.com s3 cp \
  local-photos/queens/ \
  s3://yendor/YendorCats-General-SiteAccess/queens/ \
  --recursive --exclude "*.DS_Store"

# Verify upload
aws --endpoint-url https://s3.us-west-004.backblazeb2.com s3 ls \
  s3://yendor/YendorCats-General-SiteAccess/queens/ | tail -5
```

### 3. Apply Metadata Using Python Script
```bash
# Interactive metadata setting (reads from .env)
python set_s3_metadata.py

# Or batch update via AWS CLI (example for single image)
aws --endpoint-url https://s3.us-west-004.backblazeb2.com s3api copy-object \
  --bucket yendor \
  --copy-source yendor/YendorCats-General-SiteAccess/queens/loretta-13mo-240803-1.jpg \
  --key YendorCats-General-SiteAccess/queens/loretta-13mo-240803-1.jpg \
  --metadata-directive REPLACE \
  --metadata name=Loretta,gender=F,breed="Maine Coon",category=queens,age=13
```

### 4. Validate Metadata
```bash
# Check metadata for specific image
aws --endpoint-url https://s3.us-west-004.backblazeb2.com s3api head-object \
  --bucket yendor \
  --key YendorCats-General-SiteAccess/queens/loretta-13mo-240803-1.jpg \
  | jq '.Metadata'

# Test API response includes metadata
./test-s3-metadata.sh
```

### 5. Verify Website Display
```bash
# Start development environment
make build-dev && make up-dev

# Check API endpoints return metadata
curl -s "http://localhost:5000/api/publicgallery/category/queens?includeMetadata=true" \
  | jq '.images[0] | {catName, age, breed, imageUrl}'

# Test frontend at http://localhost:5000
```

### Safety Notes
- Always test metadata updates on a single image first
- B2 copy-object operations count against API limits; batch in small groups
- Keep metadata keys concise (B2 has size limits for user metadata)
- Use `--metadata-directive REPLACE` and provide ALL desired keys in each update
- Test with staging bucket/prefix before applying to production images
