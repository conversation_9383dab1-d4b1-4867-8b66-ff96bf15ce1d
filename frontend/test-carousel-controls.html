<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carousel Controls Test - Yendor Cats</title>
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/modern-card-carousel.css">
    <style>
        body {
            padding: 2rem;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #333;
            border-radius: 8px;
        }
        .test-controls {
            margin: 1rem 0;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 0.5rem 1rem;
            background: #d4af37;
            color: #333;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }
        .test-btn:hover {
            background: #e6c547;
        }
        .debug-info {
            background: #2d2d2d;
            padding: 1rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <h1>🎠 Carousel Controls Test Page</h1>
    <p>This page tests the carousel controls functionality with debugging information.</p>

    <div class="test-section">
        <h2>Test Carousel</h2>
        <div class="modern-carousel" id="test-carousel" data-category="test">
            <div class="carousel-container">
                <div class="carousel-track">
                    <!-- Test cards -->
                    <div class="cat-card">
                        <div class="card-image">
                            <img src="https://via.placeholder.com/300x250/d4af37/333?text=Test+Card+1" alt="Test Card 1">
                            <div class="card-overlay">
                                <h3 class="cat-name">Test Card 1</h3>
                                <span class="cat-age">Test Age</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="photo-date">Test Date</span>
                        </div>
                    </div>
                    
                    <div class="cat-card">
                        <div class="card-image">
                            <img src="https://via.placeholder.com/300x250/e6c547/333?text=Test+Card+2" alt="Test Card 2">
                            <div class="card-overlay">
                                <h3 class="cat-name">Test Card 2</h3>
                                <span class="cat-age">Test Age</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="photo-date">Test Date</span>
                        </div>
                    </div>
                    
                    <div class="cat-card">
                        <div class="card-image">
                            <img src="https://via.placeholder.com/300x250/f0d858/333?text=Test+Card+3" alt="Test Card 3">
                            <div class="card-overlay">
                                <h3 class="cat-name">Test Card 3</h3>
                                <span class="cat-age">Test Age</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="photo-date">Test Date</span>
                        </div>
                    </div>
                    
                    <div class="cat-card">
                        <div class="card-image">
                            <img src="https://via.placeholder.com/300x250/fae269/333?text=Test+Card+4" alt="Test Card 4">
                            <div class="card-overlay">
                                <h3 class="cat-name">Test Card 4</h3>
                                <span class="cat-age">Test Age</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="photo-date">Test Date</span>
                        </div>
                    </div>
                    
                    <div class="cat-card">
                        <div class="card-image">
                            <img src="https://via.placeholder.com/300x250/ffed7a/333?text=Test+Card+5" alt="Test Card 5">
                            <div class="card-overlay">
                                <h3 class="cat-name">Test Card 5</h3>
                                <span class="cat-age">Test Age</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <span class="photo-date">Test Date</span>
                        </div>
                    </div>
                </div>
            </div>
            <button class="carousel-nav prev" aria-label="Previous test cards">‹</button>
            <button class="carousel-nav next" aria-label="Next test cards">›</button>
            <div class="carousel-indicators"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Manual Test Controls</h2>
        <div class="test-controls">
            <button class="test-btn" onclick="testCarousel.prev()">⬅️ Previous</button>
            <button class="test-btn" onclick="testCarousel.next()">➡️ Next</button>
            <button class="test-btn" onclick="testCarousel.testNavigation()">🧪 Auto Test</button>
            <button class="test-btn" onclick="showDebugInfo()">🔍 Debug Info</button>
            <button class="test-btn" onclick="window.testCarouselControls()">🔧 Test All</button>
        </div>
        
        <div id="debug-info" class="debug-info" style="display: none;">
            <h3>Debug Information</h3>
            <div id="debug-content"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Instructions</h2>
        <ul>
            <li><strong>Mouse:</strong> Click the ‹ and › buttons to navigate</li>
            <li><strong>Keyboard:</strong> Focus the carousel (click on it) and use arrow keys</li>
            <li><strong>Touch:</strong> Swipe left/right on mobile devices</li>
            <li><strong>Wheel:</strong> Use horizontal scroll or Shift+scroll</li>
            <li><strong>Console:</strong> Open browser console to see debug messages</li>
        </ul>
    </div>

    <script src="js/modern-card-carousel.js"></script>
    <script>
        // Initialize test carousel
        let testCarousel;
        
        document.addEventListener('DOMContentLoaded', function() {
            const carouselElement = document.getElementById('test-carousel');
            
            // Create test carousel with static data (no API calls)
            testCarousel = new ModernCardCarousel(carouselElement, {
                cardsVisible: { desktop: 3, tablet: 2, mobile: 1 },
                cardSpacing: 20,
                scrollDistance: 1,
                infinite: true,
                showMetadata: true
            });
            
            // Override loadImages to use static content
            testCarousel.images = [
                { imageUrl: 'https://via.placeholder.com/300x250/d4af37/333?text=Test+Card+1', catName: 'Test Card 1' },
                { imageUrl: 'https://via.placeholder.com/300x250/e6c547/333?text=Test+Card+2', catName: 'Test Card 2' },
                { imageUrl: 'https://via.placeholder.com/300x250/f0d858/333?text=Test+Card+3', catName: 'Test Card 3' },
                { imageUrl: 'https://via.placeholder.com/300x250/fae269/333?text=Test+Card+4', catName: 'Test Card 4' },
                { imageUrl: 'https://via.placeholder.com/300x250/ffed7a/333?text=Test+Card+5', catName: 'Test Card 5' }
            ];
            
            // Skip API loading and go straight to setup
            testCarousel.setupLayout();
            testCarousel.updateNavigation();
            testCarousel.updateIndicators();
            
            console.log('✅ Test carousel initialized');
            console.log('🎯 Available in global scope as: testCarousel');
        });
        
        function showDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            const contentDiv = document.getElementById('debug-content');
            
            if (debugDiv.style.display === 'none') {
                const info = {
                    currentIndex: testCarousel.currentIndex,
                    totalCards: testCarousel.cards.length,
                    visibleCards: testCarousel.visibleCards,
                    isTransitioning: testCarousel.isTransitioning,
                    prevButtonExists: !!testCarousel.prevButton,
                    nextButtonExists: !!testCarousel.nextButton,
                    prevButtonDisabled: testCarousel.prevButton?.disabled,
                    nextButtonDisabled: testCarousel.nextButton?.disabled,
                    counterText: testCarousel.counterEl?.textContent
                };
                
                contentDiv.innerHTML = '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
                debugDiv.style.display = 'block';
            } else {
                debugDiv.style.display = 'none';
            }
        }
    </script>
</body>
</html>
