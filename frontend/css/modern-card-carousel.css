/**
 * Modern Card Carousel Styles for Yendor Cats
 * Responsive card-based carousel with metadata display and lightbox
 */

/* --------------------------------------------------
 * Modern Carousel Container
 * -------------------------------------------------- */
.modern-carousel {
    position: relative;
    width: 100%;
    margin: 2rem 0;
    /* Enhanced for older users and accessibility */
    font-size: 16px; /* Ensure readable base font size */
    /* No overflow property - let navigation buttons extend outside if needed */
}

/* Focus indicator for keyboard navigation */
.modern-carousel:focus-within {
    outline: 3px solid #d4af37;
    outline-offset: 2px;
    border-radius: 4px;
}

.carousel-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 12px;
}

.carousel-track {
    display: flex;
    transition: transform 0.3s ease;
    align-items: stretch;
}

/* --------------------------------------------------
 * Cat <PERSON>
 * -------------------------------------------------- */
.cat-card {
    flex-shrink: 0;
    position: relative;
    background: #2d2d2d;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    margin-right: 20px;
    z-index: 1; /* Ensure cards don't interfere with navigation buttons */
}

.cat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cat-card:last-child {
    margin-right: 0;
}

/* Card Image Container */
.card-image {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.cat-card:hover .card-image img {
    transform: scale(1.05);
}

/* Card Metadata Overlay */
.card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 1.5rem 1rem 1rem;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.card-overlay .cat-name {
    font-size: 1.2rem; /* Increased for better readability */
    font-weight: 700; /* Bolder for better contrast */
    margin: 0 0 0.25rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8); /* Stronger shadow for readability */
    line-height: 1.3;
}

.card-overlay .cat-age {
    font-size: 1rem; /* Increased from 0.9rem */
    opacity: 1; /* Full opacity for better visibility */
    font-weight: 600; /* Bolder weight */
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

/* Hover Overlay with Actions */
.card-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cat-card:hover .card-hover-overlay {
    opacity: 1;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-expand-btn {
    background: rgba(111, 17, 0, 0.9);
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-primary, #333);
}

.card-expand-btn:hover {
    background: #404040;
    transform: scale(1.1);
}

/* Card Footer */
.card-footer {
    padding: 1rem;
    background: #2d2d2d;
    border-top: 1px solid #404040;
}

.photo-date {
    font-size: 0.95rem; /* Increased for better readability */
    color: #d4d4d4; /* Lighter color for better contrast on dark background */
    font-weight: 600; /* Bolder for better visibility */
    margin-bottom: 0.5rem;
}

.photo-description {
    font-size: 0.9rem;
    color: #d4d4d4;
    font-weight: 500;
    line-height: 1.4;
    margin-top: 0.5rem;
}

/* --------------------------------------------------
 * Navigation Controls
 * -------------------------------------------------- */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 64px; /* Increased from 48px for better accessibility */
    height: 64px; /* Increased from 48px for better accessibility */
    background: rgba(45, 45, 45, 0.95); /* High contrast dark background */
    border: 3px solid #ffffff; /* White border for better visibility */
    border-radius: 50%;
    font-size: 2rem; /* Increased from 1.5rem for better visibility */
    color: #ffffff; /* White color for high contrast */
    cursor: pointer;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4); /* Stronger shadow */
    transition: all 0.3s ease;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    /* Better touch target for mobile */
    min-width: 64px;
    min-height: 64px;
}

.carousel-nav:hover:not(:disabled) {
    background: rgba(212, 175, 55, 0.95); /* Golden accent on hover */
    color: #2d2d2d;
    border-color: #2d2d2d;
    transform: translateY(-50%) scale(1.15); /* Slightly larger scale */
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

.carousel-nav:active {
    transform: translateY(-50%) scale(0.95);
}

.carousel-nav:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    background: rgba(100, 100, 100, 0.5);
    border-color: rgba(150, 150, 150, 0.5);
    color: rgba(200, 200, 200, 0.5);
}

.carousel-nav.prev {
    left: 16px; /* Increased spacing for better visibility */
}

.carousel-nav.next {
    right: 16px; /* Increased spacing for better visibility */
}

/* SVG Icons for better visibility */
.carousel-nav.prev::before {
    content: '';
    width: 24px;
    height: 24px;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23ffffff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>') no-repeat center;
    background-size: contain;
}

.carousel-nav.next::before {
    content: '';
    width: 24px;
    height: 24px;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23ffffff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>') no-repeat center;
    background-size: contain;
}

.carousel-nav:hover.prev::before {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%232d2d2d" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>') no-repeat center;
    background-size: contain;
}

.carousel-nav:hover.next::before {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%232d2d2d" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>') no-repeat center;
    background-size: contain;
}

/* Numeric counter shown top-right inside each modern carousel */
.carousel-counter {
    position: absolute;
    top: 8px;
    right: 12px;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 0.85rem;
    line-height: 1;
    padding: 6px 10px;
    border-radius: 12px;
    z-index: 11;
    pointer-events: none;
}

/* Thumbnail gallery popup overlay (View All) */
.thumbnail-gallery-popup {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: none;
    flex-direction: column;
    justify-content: flex-start;
}
.thumbnail-gallery-popup.active { display: flex; }
.thumbnail-gallery-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.25rem;
    background: #2d2d2d;
    border-bottom: 1px solid #404040;
}
.thumbnail-gallery-title {
    margin: 0;
    font-size: 1.25rem;
    color: #fff;
}
.thumbnail-gallery-close {
    background: transparent;
    border: none;
    color: #fff;
    cursor: pointer;
}
.thumbnail-gallery-container {
    flex: 1;
    overflow: auto;
    padding: 1rem 1.25rem 2rem;
}
.thumbnail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
}
.thumbnail-item { cursor: pointer; border-radius: 8px; overflow: hidden; }
.thumbnail-item img { width: 100%; height: 160px; object-fit: cover; display: block; }

@media (max-width: 767px) {
    .carousel-counter { top: 6px; right: 8px; font-size: 0.8rem; }
    .thumbnail-grid { grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); }
    .thumbnail-item img { height: 120px; }
}

/* --------------------------------------------------
 * Indicators
 * -------------------------------------------------- */
.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.carousel-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: var(--accent-primary, #d4af37) 2px solid;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-indicator.active,
.carousel-indicator:hover {
    background: var(--accent-primary, #d4af37);
    border-color: var(--accent-primary, #d4af37);
}

/* --------------------------------------------------
 * Lightbox Modal
 * -------------------------------------------------- */
.cat-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cat-lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    cursor: pointer;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: #2d2d2d;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.lightbox-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    background: #404040;
}

.lightbox-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary, #333);
}

.lightbox-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    color: var(--text-secondary, #666);
}

.lightbox-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

.lightbox-body {
    display: flex;
    flex: 1;
    min-height: 0;
}

.lightbox-image-container {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #404040;
}

.lightbox-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.lightbox-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    background: var(--accent-primary, #d4af37);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: var(--text-primary, #333);
}

.lightbox-nav-btn:hover {
    background: #404040;
    transform: translateY(-50%) scale(1.1);
}

.lightbox-nav-btn.prev {
    left: 1rem;
}

.lightbox-nav-btn.next {
    right: 1rem;
}

.lightbox-metadata {
    width: 300px;
    padding: 2rem;
    background: #2d2d2d;
    border-left: 1px solid #404040;
    overflow-y: auto;
}

.metadata-item {
    margin-bottom: 1.5rem;
}

.metadata-item label {
    display: block;
    font-weight: 600;
    color: var(--text-secondary, #666);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metadata-item span {
    font-size: 1.1rem;
    color: var(--text-primary, #333);
    font-weight: 500;
}

/* --------------------------------------------------
 * Loading and Error States
 * -------------------------------------------------- */
.loading-placeholder,
.error-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    color: var(--text-secondary, #666);
}

.loading-spinner {
    margin-bottom: 1rem;
}

.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--accent-primary, #d4af37);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-placeholder p {
    font-size: 1.1rem;
    margin: 0.5rem 0;
    font-weight: 500;
}

.loading-placeholder small {
    font-size: 0.9rem;
    opacity: 0.7;
}

.error-placeholder .error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.retry-button {
    background: var(--accent-primary, #d4af37);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1rem;
    transition: background-color 0.3s ease;
}

.retry-button:hover {
    background: var(--accent-primary-dark, #b8941f);
}

/* --------------------------------------------------
 * Accessibility Enhancements for Older Users
 * -------------------------------------------------- */

/* High contrast mode improvements */
@media (prefers-contrast: high) {
    .cat-card {
        border: 3px solid #ffffff;
        background: #000000;
    }

    .carousel-nav {
        border: 4px solid #ffffff;
        background: #000000;
        color: #ffffff;
    }

    .card-overlay {
        background: rgba(0, 0, 0, 0.95);
    }

    .card-overlay .cat-name,
    .card-overlay .cat-age {
        color: #ffffff;
        text-shadow: none;
    }
}

/* Large text support */
@media (prefers-font-size: large) {
    .card-overlay .cat-name {
        font-size: 1.4rem;
    }

    .card-overlay .cat-age {
        font-size: 1.1rem;
    }

    .photo-date {
        font-size: 1.1rem;
    }

    .carousel-nav {
        font-size: 2.2rem;
        width: 72px;
        height: 72px;
    }
}

/* Touch device optimizations */
@media (pointer: coarse) {
    .carousel-nav {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .cat-card {
        margin-right: 20px; /* More spacing for easier touch navigation */
    }

    .card-expand-btn {
        width: 52px;
        height: 52px;
    }
}

/* --------------------------------------------------
 * Responsive Design
 * -------------------------------------------------- */

/* Desktop Large (1200px+) */
@media (min-width: 1200px) {
    .cat-card {
        max-width: calc(25% - 15px);
    }

    .card-image {
        height: 280px;
    }
}

/* Desktop (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .cat-card {
        max-width: calc(33.333% - 13px);
    }

    .card-image {
        height: 260px;
    }
}

/* Tablet (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .cat-card {
        max-width: calc(50% - 10px);
    }

    .card-image {
        height: 240px;
    }

    .carousel-nav {
        width: 56px; /* Larger for easier touch */
        height: 56px;
        font-size: 1.8rem;
        min-width: 56px;
        min-height: 56px;
    }

    .carousel-nav.prev {
        left: 12px;
    }

    .carousel-nav.next {
        right: 12px;
    }

    .lightbox-metadata {
        width: 250px;
        padding: 1.5rem;
    }
}

/* Mobile (767px and below) */
@media (max-width: 767px) {
    .modern-carousel {
        margin: 1rem 0;
    }

    .cat-card {
        max-width: 100%;
        margin-right: 15px;
    }

    .card-image {
        height: 220px; /* Slightly taller for better viewing */
    }

    .card-overlay {
        padding: 1rem 0.75rem 0.75rem;
    }

    .card-overlay .cat-name {
        font-size: 1.1rem; /* Larger text for better readability */
        font-weight: 700;
    }

    .card-overlay .cat-age {
        font-size: 0.9rem;
        font-weight: 600;
    }

    .card-footer {
        padding: 0.75rem;
    }

    .carousel-nav {
        width: 52px; /* Much larger for mobile touch */
        height: 52px;
        font-size: 1.5rem;
        min-width: 52px;
        min-height: 52px;
        border-width: 2px;
    }

    .carousel-nav.prev {
        left: 10px;
    }

    .carousel-nav.next {
        right: 10px;
    }

    .carousel-indicators {
        margin-top: 1rem;
    }

    .carousel-indicator {
        width: 12px; /* Larger for easier touch */
        height: 12px;
    }

    /* Lightbox adjustments for mobile */
    .lightbox-content {
        max-width: 95vw;
        max-height: 95vh;
        flex-direction: column;
    }

    .lightbox-header {
        padding: 1rem;
    }

    .lightbox-title {
        font-size: 1.25rem;
    }

    .lightbox-body {
        flex-direction: column;
    }

    .lightbox-image-container {
        min-height: 250px;
    }

    .lightbox-metadata {
        width: 100%;
        border-left: none;
        border-top: 1px solid #eee;
        padding: 1rem;
    }

    .lightbox-nav-btn {
        width: 40px;
        height: 40px;
    }

    .lightbox-nav-btn.prev {
        left: 0.5rem;
    }

    .lightbox-nav-btn.next {
        right: 0.5rem;
    }
}

/* Very small mobile (480px and below) */
@media (max-width: 480px) {
    .card-image {
        height: 200px; /* Keep reasonable height */
    }

    .card-overlay .cat-name {
        font-size: 1rem; /* Keep readable size */
        font-weight: 700;
    }

    .card-expand-btn {
        width: 44px; /* Larger touch target */
        height: 44px;
    }

    .carousel-nav {
        width: 48px; /* Still large enough for touch */
        height: 48px;
        font-size: 1.3rem;
        min-width: 48px;
        min-height: 48px;
    }

    .carousel-nav.prev {
        left: 8px;
    }

    .carousel-nav.next {
        right: 8px;
    }
}

/* --------------------------------------------------
 * Print Styles
 * -------------------------------------------------- */
@media print {
    .modern-carousel {
        break-inside: avoid;
    }

    .carousel-nav,
    .carousel-indicators,
    .cat-lightbox {
        display: none !important;
    }

    .cat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .cat-card:hover {
        transform: none;
    }
}

/* --------------------------------------------------
 * High Contrast Mode Support
 * -------------------------------------------------- */
@media (prefers-contrast: high) {
    .cat-card {
        border: 2px solid #000;
    }

    .carousel-nav {
        border: 2px solid #000;
        background: #2d2d2d;
        color: #000;
    }

    .card-overlay {
        background: rgba(0, 0, 0, 0.9);
    }
}

/* --------------------------------------------------
 * Reduced Motion Support
 * -------------------------------------------------- */
@media (prefers-reduced-motion: reduce) {
    .cat-card,
    .carousel-nav,
    .carousel-track,
    .card-image img,
    .card-overlay,
    .card-hover-overlay,
    .cat-lightbox {
        transition: none !important;
        animation: none !important;
    }

    .spinner {
        animation: none !important;
        border-top-color: transparent;
    }
}
