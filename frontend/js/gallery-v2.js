/**
 * Yendor Cats Gallery V2 - High-Performance Gallery Component
 * Implements multi-level caching, lazy loading, and performance monitoring
 * Target: 85-90% performance improvement over S3-scanning approach
 */

class GalleryV2 {
    constructor(options = {}) {
        this.options = {
            baseUrl: options.baseUrl || '/api/v2/gallery',
            fallbackUrl: options.fallbackUrl || '/api/CatGallery',
            cacheExpiry: options.cacheExpiry || 5 * 60 * 1000, // 5 minutes
            localStorageExpiry: options.localStorageExpiry || 30 * 60 * 1000, // 30 minutes
            lazyLoadThreshold: options.lazyLoadThreshold || 200,
            preloadCount: options.preloadCount || 3,
            enablePerformanceMonitoring: options.enablePerformanceMonitoring !== false,
            retryAttempts: options.retryAttempts || 3,
            retryDelay: options.retryDelay || 1000,
            ...options
        };

        // Performance monitoring
        this.performanceMetrics = {
            apiCalls: 0,
            cacheHits: 0,
            cacheMisses: 0,
            totalLoadTime: 0,
            imageLoadTimes: [],
            errors: 0
        };

        // Caching layers
        this.memoryCache = new Map();
        this.cacheTimestamps = new Map();
        
        // State management
        this.currentCategory = null;
        this.currentPage = 1;
        this.totalPages = 1;
        this.isLoading = false;
        this.loadedImages = new Set();
        this.imageObserver = null;
        
        // Event handlers
        this.eventHandlers = new Map();
        
        // Initialize
        this.init();
    }

    /**
     * Initialize the gallery component
     */
    init() {
        this.setupIntersectionObserver();
        this.setupEventListeners();
        this.startPerformanceMonitoring();
        this.cleanupExpiredCache();
        
        console.log('Gallery V2 initialized with performance monitoring');
    }

    /**
     * Setup intersection observer for lazy loading
     */
    setupIntersectionObserver() {
        const options = {
            root: null,
            rootMargin: `${this.options.lazyLoadThreshold}px`,
            threshold: 0.1
        };

        this.imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    this.imageObserver.unobserve(entry.target);
                }
            });
        }, options);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Performance monitoring events
        if (this.options.enablePerformanceMonitoring) {
            window.addEventListener('beforeunload', () => {
                this.logPerformanceMetrics();
            });
        }

        // Handle online/offline events
        window.addEventListener('online', () => {
            this.handleOnlineStatus(true);
        });

        window.addEventListener('offline', () => {
            this.handleOnlineStatus(false);
        });
    }

    /**
     * Load gallery images for a specific category
     * @param {string} category - The category to load (studs, queens, kittens, gallery)
     * @param {number} page - Page number (default: 1)
     * @param {object} options - Additional options
     */
    async loadCategory(category, page = 1, options = {}) {
        const startTime = performance.now();
        
        try {
            this.isLoading = true;
            this.currentCategory = category;
            this.currentPage = page;
            
            // Check cache first
            const cachedData = await this.getCachedData(category, page);
            if (cachedData) {
                this.performanceMetrics.cacheHits++;
                const loadTime = performance.now() - startTime;
                this.performanceMetrics.totalLoadTime += loadTime;
                
                this.renderGallery(cachedData, options);
                this.isLoading = false;
                
                console.log(`Gallery loaded from cache in ${loadTime.toFixed(2)}ms`);
                return cachedData;
            }

            // Cache miss - fetch from API
            this.performanceMetrics.cacheMisses++;
            const data = await this.fetchGalleryData(category, page, options);
            
            // Cache the data
            await this.setCachedData(category, page, data);
            
            const loadTime = performance.now() - startTime;
            this.performanceMetrics.totalLoadTime += loadTime;
            
            this.renderGallery(data, options);
            this.isLoading = false;
            
            console.log(`Gallery loaded from API in ${loadTime.toFixed(2)}ms`);
            return data;
            
        } catch (error) {
            this.performanceMetrics.errors++;
            console.error('Error loading gallery:', error);
            
            // Try fallback API
            const fallbackData = await this.loadWithFallback(category, page, options);
            if (fallbackData) {
                this.renderGallery(fallbackData, options);
            } else {
                this.renderError('Failed to load gallery. Please try again.');
            }
            
            this.isLoading = false;
            throw error;
        }
    }

    /**
     * Fetch gallery data from API with retry logic
     */
    async fetchGalleryData(category, page, options = {}) {
        const {
            sortBy = 'DateTaken',
            descending = true,
            pageSize = 12  // Increased default page size to match carousel capacity
        } = options;

        const url = `${this.options.baseUrl}/${category}?page=${page}&pageSize=${pageSize}&sortBy=${sortBy}&descending=${descending}`;
        
        for (let attempt = 1; attempt <= this.options.retryAttempts; attempt++) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                this.performanceMetrics.apiCalls++;
                
                // Extract performance headers
                const cacheSource = response.headers.get('X-Cache-Source');
                const queryTime = response.headers.get('X-Query-Time');
                
                if (cacheSource) {
                    data.cacheSource = cacheSource;
                }
                if (queryTime) {
                    data.queryTime = parseFloat(queryTime);
                }
                
                return data;
                
            } catch (error) {
                console.warn(`API attempt ${attempt} failed:`, error);
                
                if (attempt === this.options.retryAttempts) {
                    throw error;
                }
                
                await this.delay(this.options.retryDelay * attempt);
            }
        }
    }

    /**
     * Load data using fallback API
     */
    async loadWithFallback(category, page, options) {
        try {
            const fallbackUrl = `${this.options.fallbackUrl}/category/${category}`;
            const response = await fetch(fallbackUrl);
            
            if (!response.ok) {
                throw new Error(`Fallback API failed: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Transform legacy data format to V2 format
            const transformedData = {
                items: data.map(item => ({
                    id: item.id,
                    imageUrl: item.imageUrl || item.publicUrl,
                    thumbnailUrl: item.thumbnailUrl,
                    catName: item.catName,
                    catId: item.catId,
                    category: item.category,
                    title: item.title,
                    description: item.description,
                    breed: item.breed,
                    bloodline: item.bloodline,
                    gender: item.gender,
                    ageAtPhoto: item.ageAtPhoto,
                    dateTaken: item.dateTaken,
                    dateUploaded: item.dateUploaded,
                    tags: item.tags,
                    width: item.width,
                    height: item.height,
                    aspectRatio: item.aspectRatio,
                    fileSize: item.fileSize,
                    accessCount: item.accessCount || 0
                })),
                totalCount: data.length,
                page: page,
                pageSize: data.length,
                totalPages: 1,
                hasNext: false,
                hasPrevious: false,
                cacheSource: 'fallback-api',
                queryTime: 0
            };
            
            console.log(`Loaded ${data.length} images from fallback API`);
            return transformedData;
            
        } catch (error) {
            console.error('Fallback API also failed:', error);
            return null;
        }
    }

    /**
     * Render the gallery with performance optimizations
     */
    renderGallery(data, options = {}) {
        // Try to find container by ID if category is provided
        let container = options.container;
        if (!container && this.currentCategory) {
            container = document.getElementById(`${this.currentCategory}-carousel`);
        }
        if (!container) {
            container = document.querySelector('.gallery-container');
        }
        if (!container) {
            console.error('Gallery container not found for category:', this.currentCategory);
            return;
        }

        // Update pagination info
        this.totalPages = data.totalPages || 1;
        
        // Clear existing content if not appending
        if (!options.append) {
            container.innerHTML = '';
            this.loadedImages.clear();
        }

        // Create gallery grid
        const grid = this.createGalleryGrid(data.items, options);
        
        if (options.append) {
            container.appendChild(grid);
        } else {
            container.appendChild(grid);
        }

        // Update UI elements
        this.updateGalleryStats(data);
        this.updatePagination(data);
        
        // Setup lazy loading for new images
        this.setupLazyLoading(grid);
        
        // Preload next batch if needed
        this.preloadNextImages(data.items);
        
        // Trigger loaded event
        this.trigger('gallery:loaded', { data, container });
    }

    /**
     * Create optimized gallery grid
     */
    createGalleryGrid(items, options = {}) {
        const grid = document.createElement('div');
        grid.className = 'gallery-grid-v2';
        grid.setAttribute('data-category', this.currentCategory);
        
        items.forEach((item, index) => {
            const gridItem = this.createGalleryItem(item, index, options);
            grid.appendChild(gridItem);
        });
        
        return grid;
    }

    /**
     * Create individual gallery item with lazy loading
     */
    createGalleryItem(item, index, options = {}) {
        const article = document.createElement('article');
        article.className = 'gallery-item-v2';
        article.setAttribute('data-id', item.id);
        article.setAttribute('data-category', item.category);
        
        // Calculate responsive image dimensions
        const { width, height } = this.calculateResponsiveSize(item);
        
        article.innerHTML = `
            <div class="gallery-item-media">
                <img 
                    class="gallery-image lazy-load" 
                    data-src="${item.imageUrl}"
                    data-thumbnail="${item.thumbnailUrl || item.imageUrl}"
                    data-width="${width}"
                    data-height="${height}"
                    alt="${item.title || item.catName || 'Cat photo'}"
                    loading="lazy"
                    style="aspect-ratio: ${item.aspectRatio || 1}; width: 100%; height: auto;"
                />
                <div class="gallery-item-overlay">
                    <div class="gallery-item-actions">
                        <button class="btn-view" data-id="${item.id}" title="View Full Size">
                            <i class="icon-eye"></i>
                        </button>
                        <button class="btn-info" data-id="${item.id}" title="View Details">
                            <i class="icon-info"></i>
                        </button>
                    </div>
                </div>
                <div class="gallery-item-loading">
                    <div class="spinner"></div>
                </div>
            </div>
            <div class="gallery-item-details">
                <h3 class="gallery-item-title">${item.title || item.catName || 'Unnamed Cat'}</h3>
                <div class="gallery-item-meta">
                    <span class="cat-info">
                        ${item.breed || 'Maine Coon'}
                        ${item.bloodline ? ` • ${item.bloodline}` : ''}
                        ${item.gender ? ` • ${item.gender}` : ''}
                    </span>
                    ${item.ageAtPhoto ? `<span class="age-info">${item.ageAtPhoto}</span>` : ''}
                    ${item.dateTaken ? `<span class="date-info">${new Date(item.dateTaken).toLocaleDateString()}</span>` : ''}
                </div>
                ${item.description ? `<p class="gallery-item-description">${item.description}</p>` : ''}
                ${item.tags ? `<div class="gallery-item-tags">${this.renderTags(item.tags)}</div>` : ''}
            </div>
        `;
        
        // Add click handlers
        this.setupItemHandlers(article, item);
        
        return article;
    }

    /**
     * Calculate responsive image dimensions
     */
    calculateResponsiveSize(item) {
        const maxWidth = 400;
        const maxHeight = 300;
        
        let width = item.width || maxWidth;
        let height = item.height || maxHeight;
        
        // Maintain aspect ratio while fitting within max dimensions
        if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width = Math.round(width * ratio);
            height = Math.round(height * ratio);
        }
        
        return { width, height };
    }

    /**
     * Setup lazy loading for gallery items
     */
    setupLazyLoading(grid) {
        const lazyImages = grid.querySelectorAll('.lazy-load');
        
        lazyImages.forEach(img => {
            if (!this.loadedImages.has(img.dataset.src)) {
                this.imageObserver.observe(img);
            }
        });
    }

    /**
     * Load individual image with performance tracking
     */
    loadImage(imgElement) {
        const startTime = performance.now();
        const src = imgElement.dataset.src;
        const thumbnail = imgElement.dataset.thumbnail;
        
        if (this.loadedImages.has(src)) {
            return;
        }
        
        this.loadedImages.add(src);
        
        // Show loading state
        const loadingDiv = imgElement.closest('.gallery-item-v2').querySelector('.gallery-item-loading');
        if (loadingDiv) {
            loadingDiv.style.display = 'block';
        }
        
        // Create new image to preload
        const img = new Image();
        
        img.onload = () => {
            const loadTime = performance.now() - startTime;
            this.performanceMetrics.imageLoadTimes.push(loadTime);
            
            // Update the actual image
            imgElement.src = src;
            imgElement.classList.add('loaded');
            
            // Hide loading state
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
            }
            
            // Trigger loaded event
            this.trigger('image:loaded', { element: imgElement, loadTime });
        };
        
        img.onerror = () => {
            console.warn('Failed to load image:', src);
            
            // Try thumbnail as fallback
            if (thumbnail && thumbnail !== src) {
                imgElement.src = thumbnail;
                imgElement.classList.add('fallback');
            } else {
                imgElement.classList.add('error');
                imgElement.alt = 'Failed to load image';
            }
            
            // Hide loading state
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
            }
        };
        
        img.src = src;
    }

    /**
     * Preload next batch of images
     */
    preloadNextImages(items) {
        const preloadCount = Math.min(this.options.preloadCount, items.length);
        
        for (let i = 0; i < preloadCount; i++) {
            const item = items[i];
            if (item && !this.loadedImages.has(item.imageUrl)) {
                this.preloadImage(item.imageUrl);
            }
        }
    }

    /**
     * Preload single image
     */
    preloadImage(src) {
        if (this.loadedImages.has(src)) {
            return;
        }
        
        const img = new Image();
        img.onload = () => {
            this.loadedImages.add(src);
        };
        img.src = src;
    }

    /**
     * Render tags
     */
    renderTags(tags) {
        if (!tags) return '';
        
        const tagArray = typeof tags === 'string' ? tags.split(',') : tags;
        
        return tagArray.map(tag => 
            `<span class="tag">${tag.trim()}</span>`
        ).join('');
    }

    /**
     * Setup item event handlers
     */
    setupItemHandlers(article, item) {
        // View button
        const viewBtn = article.querySelector('.btn-view');
        if (viewBtn) {
            viewBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.viewImage(item);
            });
        }
        
        // Info button
        const infoBtn = article.querySelector('.btn-info');
        if (infoBtn) {
            infoBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showImageDetails(item);
            });
        }
        
        // Image click
        const img = article.querySelector('.gallery-image');
        if (img) {
            img.addEventListener('click', (e) => {
                e.preventDefault();
                this.viewImage(item);
            });
        }
    }

    /**
     * View image in lightbox/modal
     */
    viewImage(item) {
        this.trigger('image:view', { item });
        
        // Track access
        this.incrementAccessCount(item.id);
        
        // Open lightbox (to be implemented)
        console.log('View image:', item);
    }

    /**
     * Show image details
     */
    showImageDetails(item) {
        this.trigger('image:details', { item });
        console.log('Show details for:', item);
    }

    /**
     * Increment access count
     */
    async incrementAccessCount(imageId) {
        try {
            await fetch(`${this.options.baseUrl}/access/${imageId}`, {
                method: 'POST'
            });
        } catch (error) {
            console.warn('Failed to increment access count:', error);
        }
    }

    /**
     * Update gallery statistics
     */
    updateGalleryStats(data) {
        const statsElement = document.querySelector('.gallery-stats');
        if (!statsElement) return;
        
        const stats = {
            totalImages: data.totalCount || 0,
            currentPage: data.page || 1,
            totalPages: data.totalPages || 1,
            cacheSource: data.cacheSource || 'unknown',
            queryTime: data.queryTime || 0
        };
        
        statsElement.innerHTML = `
            <div class="stat-item">
                <span class="stat-label">Images:</span>
                <span class="stat-value">${stats.totalImages}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Page:</span>
                <span class="stat-value">${stats.currentPage} of ${stats.totalPages}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Source:</span>
                <span class="stat-value">${stats.cacheSource}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Query Time:</span>
                <span class="stat-value">${stats.queryTime.toFixed(0)}ms</span>
            </div>
        `;
    }

    /**
     * Update pagination
     */
    updatePagination(data) {
        const pagination = document.querySelector('.gallery-pagination');
        if (!pagination) return;
        
        const { page, totalPages, hasNext, hasPrevious } = data;
        
        pagination.innerHTML = `
            <button class="btn-page" data-action="first" ${!hasPrevious ? 'disabled' : ''}>
                <i class="icon-first"></i>
            </button>
            <button class="btn-page" data-action="prev" ${!hasPrevious ? 'disabled' : ''}>
                <i class="icon-prev"></i>
            </button>
            <span class="page-info">Page ${page} of ${totalPages}</span>
            <button class="btn-page" data-action="next" ${!hasNext ? 'disabled' : ''}>
                <i class="icon-next"></i>
            </button>
            <button class="btn-page" data-action="last" ${!hasNext ? 'disabled' : ''}>
                <i class="icon-last"></i>
            </button>
        `;
        
        // Add event listeners
        pagination.querySelectorAll('.btn-page').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.closest('.btn-page').dataset.action;
                this.handlePagination(action, { page, totalPages });
            });
        });
    }

    /**
     * Handle pagination actions
     */
    async handlePagination(action, { page, totalPages }) {
        let newPage = page;
        
        switch (action) {
            case 'first':
                newPage = 1;
                break;
            case 'prev':
                newPage = Math.max(1, page - 1);
                break;
            case 'next':
                newPage = Math.min(totalPages, page + 1);
                break;
            case 'last':
                newPage = totalPages;
                break;
        }
        
        if (newPage !== page) {
            await this.loadCategory(this.currentCategory, newPage);
        }
    }

    /**
     * Cache management
     */
    async getCachedData(category, page) {
        const cacheKey = `gallery_${category}_${page}`;
        
        // Check memory cache first
        if (this.memoryCache.has(cacheKey)) {
            const timestamp = this.cacheTimestamps.get(cacheKey);
            if (Date.now() - timestamp < this.options.cacheExpiry) {
                return this.memoryCache.get(cacheKey);
            } else {
                this.memoryCache.delete(cacheKey);
                this.cacheTimestamps.delete(cacheKey);
            }
        }
        
        // Check localStorage
        try {
            const stored = localStorage.getItem(cacheKey);
            if (stored) {
                const { data, timestamp } = JSON.parse(stored);
                if (Date.now() - timestamp < this.options.localStorageExpiry) {
                    // Store in memory cache for faster access
                    this.memoryCache.set(cacheKey, data);
                    this.cacheTimestamps.set(cacheKey, Date.now());
                    return data;
                } else {
                    localStorage.removeItem(cacheKey);
                }
            }
        } catch (error) {
            console.warn('Error reading from localStorage:', error);
        }
        
        return null;
    }

    async setCachedData(category, page, data) {
        const cacheKey = `gallery_${category}_${page}`;
        const timestamp = Date.now();
        
        // Store in memory cache
        this.memoryCache.set(cacheKey, data);
        this.cacheTimestamps.set(cacheKey, timestamp);
        
        // Store in localStorage
        try {
            const stored = JSON.stringify({ data, timestamp });
            localStorage.setItem(cacheKey, stored);
        } catch (error) {
            console.warn('Error writing to localStorage:', error);
        }
    }

    /**
     * Clean up expired cache entries
     */
    cleanupExpiredCache() {
        const now = Date.now();
        
        // Clean memory cache
        for (const [key, timestamp] of this.cacheTimestamps.entries()) {
            if (now - timestamp > this.options.cacheExpiry) {
                this.memoryCache.delete(key);
                this.cacheTimestamps.delete(key);
            }
        }
        
        // Clean localStorage
        try {
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('gallery_')) {
                    const stored = localStorage.getItem(key);
                    if (stored) {
                        const { timestamp } = JSON.parse(stored);
                        if (now - timestamp > this.options.localStorageExpiry) {
                            localStorage.removeItem(key);
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('Error cleaning localStorage:', error);
        }
    }

    /**
     * Start performance monitoring
     */
    startPerformanceMonitoring() {
        if (!this.options.enablePerformanceMonitoring) return;
        
        // Monitor every 30 seconds
        setInterval(() => {
            this.logPerformanceMetrics();
        }, 30000);
    }

    /**
     * Log performance metrics
     */
    logPerformanceMetrics() {
        const metrics = {
            ...this.performanceMetrics,
            cacheHitRate: this.performanceMetrics.cacheHits / 
                (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100,
            averageImageLoadTime: this.performanceMetrics.imageLoadTimes.length > 0 ?
                this.performanceMetrics.imageLoadTimes.reduce((a, b) => a + b) / this.performanceMetrics.imageLoadTimes.length : 0,
            averageApiTime: this.performanceMetrics.apiCalls > 0 ?
                this.performanceMetrics.totalLoadTime / this.performanceMetrics.apiCalls : 0
        };
        
        console.log('Gallery V2 Performance Metrics:', metrics);
        
        // Send to analytics if configured
        this.trigger('performance:metrics', metrics);
    }

    /**
     * Handle online/offline status
     */
    handleOnlineStatus(isOnline) {
        if (isOnline) {
            console.log('Gallery V2: Back online');
            this.trigger('gallery:online');
        } else {
            console.log('Gallery V2: Offline mode');
            this.trigger('gallery:offline');
        }
    }

    /**
     * Event system
     */
    on(eventName, handler) {
        if (!this.eventHandlers.has(eventName)) {
            this.eventHandlers.set(eventName, []);
        }
        this.eventHandlers.get(eventName).push(handler);
    }

    off(eventName, handler) {
        if (this.eventHandlers.has(eventName)) {
            const handlers = this.eventHandlers.get(eventName);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    trigger(eventName, data) {
        if (this.eventHandlers.has(eventName)) {
            this.eventHandlers.get(eventName).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${eventName}:`, error);
                }
            });
        }
    }

    /**
     * Utility methods
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Render error message
     */
    renderError(message) {
        const container = document.querySelector('.gallery-container');
        if (container) {
            container.innerHTML = `
                <div class="gallery-error">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">${message}</div>
                    <button class="btn-retry" onclick="location.reload()">Retry</button>
                </div>
            `;
        }
    }

    /**
     * Destroy gallery instance
     */
    destroy() {
        if (this.imageObserver) {
            this.imageObserver.disconnect();
        }
        
        this.memoryCache.clear();
        this.cacheTimestamps.clear();
        this.eventHandlers.clear();
        
        console.log('Gallery V2 destroyed');
    }
}

// Export for use in other modules
window.GalleryV2 = GalleryV2;
