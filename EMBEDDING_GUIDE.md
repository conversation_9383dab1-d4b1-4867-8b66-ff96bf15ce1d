# 🚀 Multi-Tier Embedding System Guide

## Overview

This guide covers the comprehensive embedding system for organizing and indexing your projects across multiple AI agents with intelligent routing and tiered quality levels.

## 🏗️ Architecture

### Multi-Tier Strategy
- **TIER 1**: Universal Index (0.6B model) - All projects for broad context
- **TIER 2**: Critical Systems (8B model) - DevOps, Infrastructure, AI
- **TIER 3**: Balanced Development (4B model) - Web apps, regular development
- **TIER 4**: Fast Processing (0.6B model) - Utilities, tools, scripts

### Collection Routing
- **Shared Collections**: Cross-agent knowledge sharing
- **Project-Specific**: Dedicated collections for major projects
- **Agent-Private**: Individual agent workspaces
- **Model-Specific**: Performance-optimized collections

## 🎯 Quick Start

### 1. Single Project Embedding

#### Using 4B Model (Balanced Quality)
```bash
cd /path/to/MCP
source embedding_env/bin/activate

# Interactive mode
python3 embed_documents.py
# Select: 2 (4B model)
# Select: 1 (claude agent)
# Select: 1 (shared collections)
# Enter path: /path/to/your/project

# Command line mode (echo inputs)
echo -e "2\n1\n1\n/path/to/your/project\ny" | python3 embed_documents.py
```

#### Using 8B Model (High Quality)
```bash
# Interactive mode
python3 embed_documents.py
# Select: 1 (8B model)
# Select: 1 (claude agent)
# Select: 1 (shared collections)
# Enter path: /path/to/your/project

# Command line mode
echo -e "1\n1\n1\n/path/to/your/project\ny" | python3 embed_documents.py
```

#### Using 0.6B Model (Fast Processing)
```bash
# Interactive mode
python3 embed_documents.py
# Select: 3 (0.6B model)
# Select: 1 (claude agent)
# Select: 1 (shared collections)
# Enter path: /path/to/your/project

# Command line mode
echo -e "3\n1\n1\n/path/to/your/project\ny" | python3 embed_documents.py
```

### 2. Batch Multi-Project Embedding

#### Strategic Configuration (Recommended)
```bash
# Run strategic multi-tier configuration
python3 batch_embed_projects.py --config strategic_projects.yaml

# Dry run first (recommended)
python3 batch_embed_projects.py --config strategic_projects.yaml --dry-run

# Process specific project tiers
python3 batch_embed_projects.py --config strategic_projects.yaml --filter "Universal"
```

#### Auto-Discovery Configuration
```bash
# Scan directory for automatic project detection
python3 batch_embed_projects.py --scan "/path/to/projects"

# Use default configuration
python3 batch_embed_projects.py

# Dry run with default config
python3 batch_embed_projects.py --dry-run
```

## 📊 Configuration Files

### Strategic Projects Configuration (`strategic_projects.yaml`)

This is the **recommended configuration** that implements the 5-tier strategy:

```yaml
projects:
  # TIER 1: Universal Index
  - name: "Universal Projects Index"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects"
    model: "dengcao/Qwen3-Embedding-0.6B:Q8_0"  # Fast, broad coverage
    type: "universal"
    sharing: "shared"
    priority: "high"

  # TIER 2: Critical Infrastructure
  - name: "DevOps & Infrastructure"
    path: "/path/to/devops/project"
    model: "dengcao/Qwen3-Embedding-8B:Q4_K_M"  # High quality
    type: "devops"
    sharing: "shared"
    priority: "high"

  # TIER 3: Web Applications
  - name: "Web Application"
    path: "/path/to/web/project"
    model: "dengcao/Qwen3-Embedding-4B:Q4_K_M"  # Balanced
    type: "web"
    sharing: "shared"
    priority: "high"
```

### Default Configuration (`embedding_projects.yaml`)

Auto-generated template for quick customization:

```yaml
projects:
  - name: "My Project"
    path: "/path/to/project"
    type: "library"  # web, devops, ai, library, client, personal
    sharing: "shared"  # shared, private, project-specific
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-4B:Q4_K_M"
    priority: "high"  # high, medium, low
    exclude_patterns: ["*.pyc", "__pycache__", ".git"]
    tags: ["custom", "project"]
```

## 🎮 Command Reference

### Basic Commands

```bash
# Single file/directory embedding (interactive)
python3 embed_documents.py

# Verbose embedding with progress
python3 run_embedding_verbose.py

# Batch processing
python3 batch_embed_projects.py

# View embedding strategy
python3 qdrant_strategy.py
```

### Advanced Options

```bash
# Configuration management
python3 batch_embed_projects.py --config custom_config.yaml
python3 batch_embed_projects.py --scan "/path/to/projects"
python3 batch_embed_projects.py --dry-run
python3 batch_embed_projects.py --report

# Individual model testing
QDRANT_URL="http://localhost:6333" python3 embed_documents.py
```

## 🔧 Model Selection Guide

### When to Use Each Model

| Model | Dimensions | Use Case | Performance | Storage |
|-------|------------|----------|-------------|---------|
| **8B** | 4096 | Critical infrastructure, AI projects, detailed documentation | Highest quality | High |
| **4B** | 2560 | Web apps, regular development, balanced workloads | Good quality | Medium |
| **0.6B** | 1024 | Universal indexing, utilities, rapid processing | Fast processing | Low |

### Model Recommendations by Project Type

- **DevOps/Infrastructure**: 8B (critical system knowledge)
- **Web Applications**: 4B (balanced development needs)
- **AI/ML Projects**: 8B (complex technical content)
- **Documentation**: 4B (good quality for reference)
- **Utilities/Scripts**: 0.6B (fast indexing)
- **Universal Index**: 0.6B (broad coverage)

## 🗂️ Collection Strategy

### Automatic Collection Routing

The system automatically routes content to appropriate collections based on:

1. **Project Type**: `devops` → `shared_knowledge_8b`, `web` → `paceyspace_projects_8b`
2. **Content Type**: `code` → `shared_code_4b`, `docs` → `shared_docs_4b`
3. **Sharing Mode**: `shared` → shared collections, `private` → agent-specific
4. **Model Used**: Ensures dimension compatibility

### Collection Types

- **`shared_knowledge_8b`**: High-quality shared knowledge (4096 dims)
- **`shared_docs_4b`**: General documentation (2560 dims)
- **`shared_code_4b`**: Shared codebase knowledge (2560 dims)
- **`paceyspace_projects_8b`**: Project-specific collections (4096 dims)
- **`search_qwen3_*`**: Fast search collections per model
- **`{agent}_conversations_4b`**: Private agent conversations
- **`{agent}_workspace_0_6b`**: Agent workspace indexing

## 📈 Monitoring and Reporting

### System Status
```bash
# Collection report
python3 batch_embed_projects.py --report

# Strategy overview
python3 qdrant_strategy.py

# Collection statistics
python3 -c "
from embed_documents import DocumentEmbedder
embedder = DocumentEmbedder()
collections = embedder.list_collections()
for col in collections:
    print(f'{col[\"name\"]}: {col[\"points_count\"]} points')
"
```

### Progress Monitoring
```bash
# Verbose embedding with real-time progress
python3 run_embedding_verbose.py

# Background processing with monitoring
python3 batch_embed_projects.py &
# Monitor with: tail -f /path/to/logs
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Point ID Errors
```
Error: value X is not a valid point ID
```
**Solution**: Point ID issue fixed in latest version. Update `embed_documents.py` with proper integer conversion.

#### 2. Dimension Mismatches
```
Error: vector dimension mismatch
```
**Solution**: Ensure collection was created with correct model dimensions. Delete and recreate collection if needed.

#### 3. Ollama Connection Issues
```
Error: connection refused to Ollama
```
**Solution**:
```bash
# Start Ollama service
ollama serve

# Verify models are available
ollama list

# Pull required models if missing
ollama pull dengcao/Qwen3-Embedding-8B:Q4_K_M
ollama pull dengcao/Qwen3-Embedding-4B:Q4_K_M
ollama pull dengcao/Qwen3-Embedding-0.6B:Q8_0
```

#### 4. Qdrant Connection Issues
```bash
# Start local Qdrant
docker run -p 6333:6333 qdrant/qdrant

# Test connection
curl http://localhost:6333/healthz

# Check collections
curl http://localhost:6333/collections
```

### Performance Optimization

#### For Large Projects
```bash
# Use fast model for initial indexing
echo -e "3\n1\n1\n/large/project\ny" | python3 embed_documents.py

# Then selective high-quality embedding for critical files
echo -e "1\n1\n1\n/large/project/critical\ny" | python3 embed_documents.py
```

#### Memory Management
```bash
# Process projects in batches
python3 batch_embed_projects.py --config small_batch.yaml

# Monitor system resources
htop # or Activity Monitor on macOS
```

## 🎯 Best Practices

### 1. Start with Universal Index
Always begin with the Universal Projects Index (0.6B model) to get broad contextual awareness across all projects.

### 2. Strategic Model Selection
- Use 8B for critical systems and infrastructure
- Use 4B for regular development work
- Use 0.6B for utilities and broad indexing

### 3. Incremental Processing
Process projects in tiers by priority:
1. Universal index (all projects)
2. Critical infrastructure (DevOps, security)
3. Active development projects
4. Reference and archive projects

### 4. Configuration Management
- Keep multiple configuration files for different scenarios
- Use descriptive names and tags
- Regularly update exclude patterns

### 5. Monitoring
- Run dry-runs before large operations
- Monitor collection sizes and performance
- Regular reports to track system growth

## 📚 Examples

### Example 1: New Web Project
```bash
# Quick 4B embedding for new web project
echo -e "2\n1\n1\n/path/to/new/web/project\ny" | python3 embed_documents.py
```

### Example 2: Critical Infrastructure Update
```bash
# High-quality 8B embedding for infrastructure
echo -e "1\n1\n1\n/path/to/infrastructure\ny" | python3 embed_documents.py
```

### Example 3: Batch Processing Multiple Projects
```bash
# Strategic configuration for comprehensive coverage
python3 batch_embed_projects.py --config strategic_projects.yaml --dry-run
python3 batch_embed_projects.py --config strategic_projects.yaml
```

### Example 4: Custom Configuration
Create `my_projects.yaml`:
```yaml
projects:
  - name: "My Critical System"
    path: "/path/to/critical/system"
    model: "dengcao/Qwen3-Embedding-8B:Q4_K_M"
    type: "devops"
    sharing: "shared"
    agent: "claude"
    priority: "high"
    tags: ["critical", "production"]
```

Then run:
```bash
python3 batch_embed_projects.py --config my_projects.yaml
```

## 🔄 Workflow Integration

### Daily Development
1. Universal index covers all projects for context
2. Use 4B model for active development projects
3. Use 8B model for critical infrastructure changes

### Project Onboarding
1. Add new project to appropriate tier in configuration
2. Run batch embedding with strategic config
3. Verify collections and routing

### Maintenance
1. Regular reports to monitor system health
2. Clean up old/unused collections
3. Update configurations as projects evolve

---

## 📞 Support

For issues or questions:
1. Check troubleshooting section above
2. Verify Ollama and Qdrant services are running
3. Test with dry-run mode first
4. Review configuration files for syntax errors

**Happy Embedding! 🚀**