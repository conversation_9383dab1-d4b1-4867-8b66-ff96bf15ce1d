# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Project Overview

This repository contains MCP (Model Context Protocol) server configurations and bridges for PaceySpace, focusing on AI agent integration and vector database operations. The project includes Ollama bridge services, MCP server configurations, and multi-agent workflow setups for self-hosted infrastructure.

## Architecture

### Core Components

- **Ollama Bridge** (`MCP/ollama-bridge/`): Node.js Express server that provides HTTP API endpoints for Ollama model interactions
- **MCP Server Configuration** (`MCP/cline_mcp_settings.json`): Configuration for Cline MCP integration with Ollama
- **Agent Configuration** (`agent-configuration-guide.md`): Multi-agent workflow setup and approval automation
- **Infrastructure Documentation** (`Docs/`): Proxmox VE, Qdrant vector database, and remote development setup

### Technology Stack

- **Runtime**: Node.js with ES modules
- **Web Framework**: Express.js with CORS support
- **AI Integration**: Ollama local models (default: llama3.1:8b)
- **Vector Database**: Qdrant for embeddings and semantic search
- **Infrastructure**: Proxmox VE with LXC containers
- **Development**: VSCode Remote SSH, Docker containers

### Data Flow

1. **Local Development**: MacBook Pro M3 runs Qwen3 Embed 4B for local embeddings
2. **Remote Processing**: Proxmox VE server hosts Qdrant vector database
3. **Bridge Services**: Ollama bridge provides HTTP API for model completions
4. **MCP Integration**: Cline and other tools connect via MCP protocol
5. **Agent Coordination**: Multi-agent workflows with auto-approval settings

## Development Commands

### Ollama Bridge Server

```bash
# Start the Ollama bridge server
cd MCP/ollama-bridge/
npm start

# Install dependencies (if needed)
npm install

# Health check
curl http://localhost:3000/health

# Test completion endpoint
curl -X POST http://localhost:3000/complete \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello world", "model": "llama3.1:8b", "maxTokens": 50}'

# List available models
curl http://localhost:3000/models
```

### MCP Server Operations

```bash
# Start Qdrant vector database (Docker)
docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant:latest

# Health check Qdrant
curl -sS http://localhost:6333/healthz

# Run MCP server with in-memory Qdrant (for testing)
QDRANT_URL=":memory:" COLLECTION_NAME="test" \
fastmcp dev src/mcp_server_qdrant/server.py

# Run MCP server with local Qdrant instance
QDRANT_URL="http://localhost:6333" \
COLLECTION_NAME="my-collection" \
uvx mcp-server-qdrant

# Run with SSE transport on custom port
QDRANT_URL="http://localhost:6333" \
COLLECTION_NAME="my-collection" \
FASTMCP_PORT=1234 \
uvx mcp-server-qdrant --transport sse
```

### Proxmox Development Environment

```bash
# Connect to Proxmox for remote development
ssh proxmox-mcp

# Create LXC container for MCP server
pct create 100 local:vztmpl/ubuntu-22.04-standard_22.04-1_amd64.tar.zst \
  --hostname mcp-server \
  --memory 4096 \
  --cores 4 \
  --rootfs local-lvm:32 \
  --net0 name=eth0,bridge=vmbr0,ip=dhcp \
  --unprivileged 1
```

### Testing and Debugging

```bash
# Test Ollama connectivity
curl http://127.0.0.1:11434/api/tags

# Run MCP Inspector (UI on http://localhost:5173)
QDRANT_URL=":memory:" COLLECTION_NAME="test" \
fastmcp dev src/mcp_server_qdrant/server.py

# Check system status
systemctl status mcp-server
```

## Key Configuration Files

### MCP Server Settings
- `MCP/cline_mcp_settings.json`: Cline MCP configuration with auto-approval enabled
- Environment variables: `QDRANT_URL`, `COLLECTION_NAME`, `QDRANT_API_KEY`, `EMBEDDING_MODEL`

### Agent Configuration
- Auto-approval enabled for streamlined multi-agent workflows
- Roocodes configuration in VS Code settings for approval bypass
- Docker-based agent isolation and coordination

### Infrastructure
- SSH key-based authentication for Proxmox access
- Firewall rules restricting access to specific IPs
- Container resource limits and network segmentation

## Important Guidelines

### Agent Behavior Standards
- Follow PARA method organization (Projects, Areas, Resources, Archive)
- Use WikiLink syntax `[[WikiLink]]` for internal references
- Include comprehensive YAML frontmatter in documentation
- Structure memory entries as `[CATEGORY] Information`

### Security Practices
- Use SSH key authentication (ed25519 preferred)
- Implement firewall rules for access control
- Run unprivileged LXC containers
- Network segmentation with dedicated VLANs
- Regular security updates and credential rotation

### Documentation Requirements
- Compatible with Obsidian markdown format
- Include metadata tags: `paceyspace`, `brain-preservatives`, topic keywords
- Professional, clear language for clients and internal teams
- Show evidence of methods and best practices

### Multi-Agent Workflows
- Environment isolation using Docker containers
- Shared state management via Redis or database
- Event-driven communication patterns
- Centralized logging with ELK stack
- Resource management and load balancing

## Common Troubleshooting

### Ollama Connection Issues
- Ensure Ollama is running on `http://127.0.0.1:11434`
- Check model availability with `/api/tags` endpoint
- Verify firewall rules allow local connections

### Qdrant Connection Problems
- Start Qdrant instance or use `QDRANT_URL=":memory:"` for testing
- Ensure environment variables are properly set
- Check collection name configuration
- Verify network connectivity between services

### MCP Server Configuration
- Ensure only one of `QDRANT_URL` or `QDRANT_LOCAL_PATH` is set
- Use `--transport sse` for remote client connections
- Check `FASTMCP_PORT` and `FASTMCP_HOST` settings for Docker
- Verify auto-approval settings in agent configurations

### Remote Development
- Confirm SSH key authentication is working
- Check VSCode Remote SSH extension configuration
- Verify Proxmox container network settings
- Test connectivity with basic commands before complex operations