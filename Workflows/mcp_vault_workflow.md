---
creation_date: 2025-09-18
modification_date: 2025-09-18
type: runbook
aliases: [MCP Vault Workflow, Vault Secrets Guide]
tags: [paceyspace, mcp, vault, secrets, workflow, runbook, automation]
status: active
version: 1.0
author: Gemini
reviewers: [<PERSON>]
related: [launch-claude-with-env.sh, AGENTS.MD]
org: PaceySpace
divisions: [Labs]
vault: brain-preservatives
area: documentation
platforms: [obsidian, vscode, shell]
infrastructure: [on-prem, bare-metal, homelab]
---

# Runbook: Using Vault for MCP Secrets

This document outlines the workflow for managing API keys and other secrets for MCP (Multi-agent Control Plane) services using HashiCorp Vault.

## Objective

To centralize and securely manage secrets for MCP services, removing the need to store them directly in the `.env` file.

---

## How It Works: The Automated Workflow

The `launch-claude-with-env.sh` script now contains all the logic to automatically inject secrets from <PERSON>ault into the environment.

1.  You run `./launch-claude-with-env.sh`.
2.  The script reads your `VAULT_TOKEN` from the `.env` file.
3.  It connects to your local Vault server.
4.  It fetches the required API keys (e.g., `BRAVE_API_KEY`) from their designated paths in Vault.
5.  It exports these keys as environment variables.
6.  Finally, it launches the Claude Desktop app. The MCP servers started by Claude will inherit these environment variables and be ready to use.

**The only secret you need in your `.env` file for this to work is the `VAULT_TOKEN`.**

---

## How to Add a New Secret for a New Service

Let's say you have a new service, "NewService," that needs an API key.

### Step 1: Store the New Secret in Vault

First, you need to add the secret to Vault. You will need your **Root Token** for this write operation.

Open a terminal and run the following command, replacing the path and values as needed:

```bash
# Make sure you have your ROOT token for this, not the read-only one
export VAULT_TOKEN="your-root-token-here"
export VAULT_ADDR="http://127.0.0.1:8200"

# Command to store the secret
vault kv put mcp/new-service api_key="the-actual-api-key-for-new-service"
```

### Step 2: Modify the Launch Script

Next, you need to tell the `launch-claude-with-env.sh` script to fetch this new secret.

1.  **Open the script:** `MCP/launch-claude-with-env.sh`
2.  **Find the Vault section:** Locate the block of code that starts with `# Fetch secrets from Vault...`.
3.  **Add a line for your new secret:** Add a new `export` line for your `NEW_SERVICE_API_KEY` inside the `if [ -n "$VAULT_TOKEN" ]; then` block.

**Example:**

```bash
# ... inside the launch script ...

if [ -n "$VAULT_TOKEN" ]; then
    print_status "VAULT_TOKEN is set. Fetching secrets..."
    # Ensure VAULT_ADDR is set, default if not
    VAULT_ADDR=${VAULT_ADDR:-http://127.0.0.1:8200}

    # Fetch Brave API Key (existing)
    export BRAVE_API_KEY=$(VAULT_ADDR="$VAULT_ADDR" VAULT_TOKEN="$VAULT_TOKEN" vault kv get -field=api_key mcp/brave-search)
    
    # ADD THIS LINE FOR YOUR NEW SERVICE
    export NEW_SERVICE_API_KEY=$(VAULT_ADDR="$VAULT_ADDR" VAULT_TOKEN="$VAULT_TOKEN" vault kv get -field=api_key mcp/new-service)

    # ... status checks for the keys ...
    if [ -n "$NEW_SERVICE_API_KEY" ]; then
        print_success "✓ Successfully fetched NEW_SERVICE_API_KEY from Vault"
    fi
else
# ...
```

That's it. The next time you run the launch script, it will also fetch and inject the `NEW_SERVICE_API_KEY`.

---

## Troubleshooting

### Is Vault Running?

The Vault dev server is running as a background process. To check its status, run:

```bash
curl http://127.0.0.1:8200/v1/sys/health
```

If it's running, you will see a JSON response. If it fails, the server is not running. You can restart it with:

```bash
vault server -dev > /dev/null 2>&1 &
```
*(Note: Restarting the dev server will generate a **new** root token and a new read-only token, which you would need to update in your `.env` file.)*

### "Failed to fetch... from Vault" warning

If you see this warning when running the launch script, check the following:
1.  Is the Vault server running? (Use the `curl` command above).
2.  Is the `VAULT_TOKEN` in your `.env` file correct and does it have the `mcp-readonly` policy?
3.  Does the secret actually exist at the path you specified in Vault? You can check with `vault kv get mcp/your-service-path`.
