---
id: agent-collaboration
title: "AGENT-COLLABORATION.md — Multi-Agent Coordination System"
description: "Real-time coordination file for multiple AI agents working on the YendorCats codebase simultaneously. Prevents conflicts and enables collaborative development."
company: "PaceySpace"
author: "<PERSON> Pacey"
owner: "<EMAIL>"
version: "1.0.0"
created: "2025-09-16"
updated: "2025-09-16"
status: "active"
type: "coordination/collaboration"
project: "yendorcats"
area: "engineering/devops"
agents_supported: ["Cline", "Roo Code", "Augment Code", "Claude", "GPT-4", "Custom Agents"]
collaboration_method: "file-based coordination"
embedding_integration: true
real_time_updates: true
requires_metadata: true
tags:
  - agents
  - collaboration
  - coordination
  - real-time
  - multi-agent
  - conflict-prevention
  - embeddings
  - qdrant
  - qwen3
aliases: ["Agent Coordination", "Multi-Agent System", "Collaboration File"]
---

# 🤖 Multi-Agent Collaboration System

## Purpose
This file serves as a **real-time coordination hub** for multiple AI agents working on the YendorCats codebase simultaneously. It prevents conflicts, enables task coordination, and ensures efficient collaborative development.

## 🔄 How It Works

### 1. Task Declaration
When an agent starts working on a task, it **adds an entry** to the [Current Active Tasks](#current-active-tasks) section below.

### 2. Work Completion
When an agent completes its work, it **removes its entry** from the active tasks section.

### 3. Conflict Prevention
Before starting work, agents should:
- Check this file for conflicting tasks
- Review updated embeddings in Qdrant for context
- Declare their intended work area

### 4. Real-Time Context Sharing
All agents have access to:
- **Qdrant Vector Database**: `http://localhost:6333`
- **Collections**: `ws-74d1bf96e6331b95` (1536-dim overview), `search_qwen3_0_6b` (1024-dim fast)
- **Embedding Model**: `dengcao/Qwen3-Embedding-8B:Q4_K_M` (via Ollama)
- **MCP Server**: Local Qdrant MCP for[docker-compose.dev.yml](docker-compose.dev.yml) real-time context retrieval

## 📋 Current Active Tasks

> **Instructions for Agents:**
> - Add your task when you start working
> - Include: Agent name, task description, files involved, estimated completion time
> - Remove your entry when completed
> - Use the format below

### Template Entry
```markdown
**Agent**: [Agent Name/Type]
**Task**: [Brief description of what you're working on]
**Files**: [List of files being modified]
**Started**: [YYYY-MM-DD HH:MM]
**ETA**: [Expected completion time]
**Status**: [In Progress/Blocked/Testing]
```

---

### 🚧 ACTIVE WORK AREA - AGENTS ADD/REMOVE ENTRIES HERE 🚧

<!-- Task completed - embedding system configured and indexing in progress -->

---

## 🔍 Embedding System Status

### Current Configuration
- **Vector Database**: Qdrant (localhost:6333)
- **Collection**: `yendorcats_docs`
- **Embedding Model**: `dengcao/Qwen3-Embedding-8B:Q4_K_M`
- **Dimensions**: 4096
- **Distance Metric**: Cosine similarity
- **Chunk Size**: 1000 characters
- **Chunk Overlap**: 200 characters

### Last Indexing Status
- **Status**: Currently indexing (520/1618 files processed)
- **Last Update**: 2025-09-16 03:15
- **Total Documents**: ~520 (in progress)
- **Total Chunks**: ~3000+ (in progress)

> **Note**: Agents should update this section after running embedding operations

## 🛠️ Agent Guidelines

### Before Starting Work
1. **Check Active Tasks**: Review current work to avoid conflicts
2. **Query Embeddings**: Use MCP server to get latest context
3. **Declare Intent**: Add your task to the active tasks section
4. **Check Dependencies**: Ensure your work doesn't conflict with others

### During Work
1. **Update Status**: Modify your entry if timeline changes
2. **Communicate Blockers**: Update status if blocked
3. **Respect Boundaries**: Don't modify files another agent is working on

### After Completion
1. **Remove Entry**: Delete your task from active tasks
2. **Update Embeddings**: Re-run indexing if you've made significant changes
3. **Update Status**: Modify embedding system status if relevant

## 🔧 Technical Integration

### MCP Server Configuration
```json
{
  "mcpServers": {
    "qdrant-local": {
      "command": "uvx",
      "args": ["mcp-server-qdrant"],
      "env": {
        "QDRANT_URL": "http://localhost:6333",
        "COLLECTION_NAME": "yendorcats_docs",
        "EMBEDDING_MODEL": "ollama/dengcao/Qwen3-Embedding-8B:Q4_K_M",
        "OLLAMA_URL": "http://localhost:11434"
      }
    }
  }
}
```

### Quick Commands for Agents
```bash
# Check Qdrant status
curl http://localhost:6333/collections/yendorcats_docs

# Run embedding update
python embed_content.py

# Test embeddings
python test_embeddings.py

# Check Ollama models
ollama list | grep -i qwen
```

### Qdrant via Docker Compose (dev)
- Service file: `docker-compose.qdrant.yml`
- Data: Docker named volume `yendorcats_qdrant_data` (kept outside the repo)
- Avoid: host bind like `./qdrant_storage:/qdrant/storage`

```bash
# Start/stop Qdrant
make qdrant-up
make qdrant-down

# Health and collections
make qdrant-health
make qdrant-collections
```

## 📊 Collaboration Metrics

### Success Indicators
- [ ] No file conflicts between agents
- [ ] Embeddings stay current (updated within 1 hour of major changes)
- [ ] All agents can access shared context via MCP
- [ ] Task completion times meet estimates

### Warning Signs
- Multiple agents modifying same files
- Embeddings more than 2 hours out of date
- MCP server connection failures
- Agents working without declaring tasks

## 🚨 Emergency Procedures

### If Conflicts Occur
1. **Stop Work**: Immediately cease modifications
2. **Communicate**: Add "BLOCKED" status to your task
3. **Coordinate**: Use this file to negotiate resolution
4. **Resume**: Only after conflict is resolved

### If Embeddings Fail
1. **Check Ollama**: Ensure `dengcao/Qwen3-Embedding-8B:Q4_K_M` is available
2. **Check Qdrant**: Verify localhost:6333 is accessible
3. **Restart Services**: Restart Ollama and Qdrant if needed
4. **Re-index**: Run full embedding process

## 📝 Change Log

### 2025-09-16
- Initial creation of collaboration system
- Configured Qwen3 8B Q4 embedding model
- Set up MCP server integration
- Established task coordination protocol

---

### Tags
#agents #collaboration #coordination #real-time #multi-agent #conflict-prevention #embeddings #qdrant #qwen3 #mcp-server #ollama

---
