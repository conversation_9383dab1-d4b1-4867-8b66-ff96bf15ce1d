services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant
    restart: unless-stopped
    ports:
      - "127.0.0.1:6333:6333"
      - "127.0.0.1:6334:6334"
    volumes:
      - yendorcats_qdrant_data:/qdrant/storage
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:6333/ready"]
      interval: 5s
      timeout: 3s
      retries: 30
      start_period: 5s

volumes:
  yendorcats_qdrant_data:
    external: true
    name: yendorcats_qdrant_data
