# Makefile for YendorCats Docker ECR Deployment

# Configuration
AWS_REGION ?= us-east-1
AWS_ACCOUNT_ID := $(shell aws sts get-caller-identity --query Account --output text 2>/dev/null)
ECR_REGISTRY := $(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_REGION).amazonaws.com
PROJECT_NAME := yendorcats

# Check if AWS_ACCOUNT_ID was successfully retrieved
ifeq ($(AWS_ACCOUNT_ID),)
$(error AWS CLI not configured or unable to get account ID)
endif

# Service definitions
SERVICES := api uploader
API_IMAGE := $(ECR_REGISTRY)/$(PROJECT_NAME)/api
UPLOADER_IMAGE := $(ECR_REGISTRY)/$(PROJECT_NAME)/uploader

.PHONY: help
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Available targets:'
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "  %-20s %s\n", $$1, $$2}'

.PHONY: ecr-login
ecr-login: ## Login to ECR
	@echo "Logging in to ECR..."
	@aws ecr get-login-password --region $(AWS_REGION) | docker login --username AWS --password-stdin $(ECR_REGISTRY)

.PHONY: ecr-create-repos
ecr-create-repos: ## Create ECR repositories if they don't exist
	@for service in $(SERVICES); do \
		echo "Checking ECR repository $(PROJECT_NAME)/$$service..."; \
		aws ecr describe-repositories --repository-names "$(PROJECT_NAME)/$$service" --region $(AWS_REGION) >/dev/null 2>&1 || \
		(echo "Creating repository $(PROJECT_NAME)/$$service..." && \
		 aws ecr create-repository --repository-name "$(PROJECT_NAME)/$$service" --region $(AWS_REGION)); \
	done

.PHONY: build
build: ## Build all services locally
	@echo "Building services with docker-compose..."
	@docker-compose -f docker-compose.production.yml build

.PHONY: local-test-setup
local-test-setup: ## Setup local testing environment
	@echo "Setting up local testing environment..."
	@./scripts/setup-local-test.sh

.PHONY: local-test
local-test: ## Deploy local testing environment (mirrors production)
	@echo "Deploying local testing environment..."
	@./scripts/deploy-local-test.sh deploy

.PHONY: local-test-stop
local-test-stop: ## Stop local testing environment
	@echo "Stopping local testing environment..."
	@./scripts/deploy-local-test.sh stop

.PHONY: local-test-logs
local-test-logs: ## View local testing logs
	@./scripts/deploy-local-test.sh logs

.PHONY: local-test-status
local-test-status: ## Show local testing status
	@./scripts/deploy-local-test.sh status

.PHONY: local-test-health
local-test-health: ## Run health checks on local testing environment
	@./scripts/deploy-local-test.sh health

.PHONY: build-api
build-api: ## Build API service
	@echo "Building API service..."
	@docker build -t $(PROJECT_NAME)/api:latest -f backend/YendorCats.API/Dockerfile .

.PHONY: build-uploader
build-uploader: ## Build uploader service
	@echo "Building uploader service..."
	@docker build -t $(PROJECT_NAME)/uploader:latest -f tools/file-uploader/Dockerfile tools/file-uploader

.PHONY: tag
tag: ## Tag images for ECR
	@echo "Tagging images for ECR..."
	@docker tag $(PROJECT_NAME)/api:latest $(API_IMAGE):latest
	@docker tag $(PROJECT_NAME)/api:latest $(API_IMAGE):$(shell date +%Y%m%d-%H%M%S)
	@docker tag $(PROJECT_NAME)/uploader:latest $(UPLOADER_IMAGE):latest
	@docker tag $(PROJECT_NAME)/uploader:latest $(UPLOADER_IMAGE):$(shell date +%Y%m%d-%H%M%S)

.PHONY: push
push: ecr-login ## Push images to ECR
	@echo "Pushing API to ECR..."
	@docker push $(API_IMAGE):latest
	@docker push $(API_IMAGE):$(shell date +%Y%m%d-%H%M%S)
	@echo "Pushing Uploader to ECR..."
	@docker push $(UPLOADER_IMAGE):latest
	@docker push $(UPLOADER_IMAGE):$(shell date +%Y%m%d-%H%M%S)

.PHONY: deploy
deploy: ecr-create-repos build tag push ## Full deployment: create repos, build, tag, and push
	@echo "✅ Deployment complete!"
	@echo "Images pushed to:"
	@echo "  - $(API_IMAGE):latest"
	@echo "  - $(UPLOADER_IMAGE):latest"

.PHONY: pull
pull: ecr-login ## Pull latest images from ECR
	@echo "Pulling latest images from ECR..."
	@docker pull $(API_IMAGE):latest
	@docker pull $(UPLOADER_IMAGE):latest
	@docker tag $(API_IMAGE):latest $(PROJECT_NAME)/api:latest
	@docker tag $(UPLOADER_IMAGE):latest $(PROJECT_NAME)/uploader:latest

.PHONY: run-ecr
run-ecr: pull ## Run services using ECR images
	@echo "Running services from ECR..."
	@AWS_ACCOUNT_ID=$(AWS_ACCOUNT_ID) AWS_REGION=$(AWS_REGION) docker-compose -f docker-compose.ecr.yml up -d

.PHONY: stop
stop: ## Stop all services
	@docker-compose -f docker-compose.production.yml down

.PHONY: clean
clean: ## Remove local images
	@echo "Removing local images..."
	@docker rmi $(PROJECT_NAME)/api:latest $(PROJECT_NAME)/uploader:latest 2>/dev/null || true
	@docker rmi $(API_IMAGE):latest $(UPLOADER_IMAGE):latest 2>/dev/null || true

.PHONY: logs
logs: ## Show logs from running services
	@docker-compose -f docker-compose.production.yml logs -f

.PHONY: status
status: ## Show status of services
	@docker-compose -f docker-compose.production.yml ps

# Development helpers
.PHONY: build-dev
build-dev: ## Build services for local development
	@docker-compose -f docker-compose.yml build

.PHONY: up-dev
up-dev: ## Start services for local development
	@docker-compose -f docker-compose.yml up -d

.PHONY: down-dev
down-dev: ## Stop development services
	@docker-compose -f docker-compose.yml down

# ---------------------------
# Qdrant (dev) management
# ---------------------------
QDRANT_COMPOSE := docker-compose.qdrant.yml

.PHONY: qdrant-up
qdrant-up: ## Start local Qdrant (uses named volume yendorcats_qdrant_data)
	@docker compose -f $(QDRANT_COMPOSE) up -d qdrant

.PHONY: qdrant-down
qdrant-down: ## Stop local Qdrant
	@docker compose -f $(QDRANT_COMPOSE) down

.PHONY: qdrant-logs
qdrant-logs: ## Tail Qdrant logs
	@docker compose -f $(QDRANT_COMPOSE) logs -f qdrant

.PHONY: qdrant-health
qdrant-health: ## Check Qdrant /ready endpoint
	@curl -sf http://localhost:6333/ready && echo "Qdrant ready" || (echo "Qdrant not ready" && exit 1)

.PHONY: qdrant-collections
qdrant-collections: ## List Qdrant collections
	@curl -s http://localhost:6333/collections | jq .
