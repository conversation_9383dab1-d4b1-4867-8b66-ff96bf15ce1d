projects:
  # TIER 1: Universal Projects Index - All projects for broad context
  - name: "Universal Projects Index"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects"
    type: "universal"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-0.6B:Q8_0"
    priority: "high"
    exclude_patterns:
      - "node_modules"
      - ".git"
      - "__pycache__"
      - "*.pyc"
      - ".venv"
      - ".env"
      - "dist"
      - "build"
      - ".DS_Store"
      - "*.log"
      - ".terraform"
      - "*.tfstate"
      - "backup*"
      - "*.bak"
    include_patterns:
      - "*.md"
      - "*.txt"
      - "*.py"
      - "*.js"
      - "*.ts"
      - "*.json"
      - "*.yaml"
      - "*.yml"
      - "README*"
      - "package.json"
      - "requirements.txt"
      - "*.tf"
      - "Dockerfile"
      - "docker-compose.yml"
    tags: ["universal", "projects-overview", "context"]

  # TIER 2: DevOps & Infrastructure - High quality embeddings
  - name: "DevOps & Infrastructure"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/hardening"
    type: "devops"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-8B:Q4_K_M"
    priority: "high"
    exclude_patterns:
      - ".git"
      - "__pycache__"
      - ".DS_Store"
    tags: ["devops", "infrastructure", "hardening", "security"]

  - name: "MCP Infrastructure"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/MCP"
    type: "devops"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-8B:Q4_K_M"
    priority: "high"
    exclude_patterns:
      - "node_modules"
      - ".git"
      - "__pycache__"
      - ".venv"
      - ".DS_Store"
    tags: ["mcp", "infrastructure", "embedding", "ai"]

  - name: "PaceySpace Cluster"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/paceyspace-cluster"
    type: "devops"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-8B:Q4_K_M"
    priority: "high"
    exclude_patterns:
      - ".terraform"
      - "*.tfstate"
      - ".git"
      - "__pycache__"
      - ".DS_Store"
    tags: ["kubernetes", "cluster", "infrastructure", "devops"]

  # TIER 3: Web Applications - Balanced quality
  - name: "Cabreguca Website"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/cabreguca-website"
    type: "web"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-4B:Q4_K_M"
    priority: "high"
    exclude_patterns:
      - "node_modules"
      - "dist"
      - "build"
      - ".git"
      - "__pycache__"
      - ".DS_Store"
    tags: ["web", "website", "frontend"]

  - name: "YendorCats Project"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats"
    type: "web"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-4B:Q4_K_M"
    priority: "high"
    exclude_patterns:
      - "node_modules"
      - "dist"
      - "build"
      - ".git"
      - "__pycache__"
      - ".DS_Store"
    tags: ["web", "yendorcats", "frontend", "wordpress"]

  - name: "PaceySpace Branding"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/paceyspace-branding"
    type: "web"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-4B:Q4_K_M"
    priority: "medium"
    exclude_patterns:
      - ".git"
      - ".DS_Store"
    tags: ["branding", "design", "web"]

  # TIER 4: AI & Machine Learning - High quality
  - name: "Ollama Projects"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/ollama"
    type: "ai"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-8B:Q4_K_M"
    priority: "medium"
    exclude_patterns:
      - ".git"
      - "__pycache__"
      - "*.pyc"
      - ".venv"
      - ".DS_Store"
    tags: ["ai", "ollama", "llm", "machine-learning"]

  - name: "OpenWebUI Ollama"
    path: "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/open_web_ui-ollama"
    type: "ai"
    sharing: "shared"
    agent: "claude"
    model: "dengcao/Qwen3-Embedding-8B:Q4_K_M"
    priority: "medium"
    exclude_patterns:
      - "node_modules"
      - ".git"
      - "__pycache__"
      - ".venv"
      - ".DS_Store"
    tags: ["ai", "ollama", "web-ui", "interface"]